{"name": "vinkey-web-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "format": "prettier . --check", "format:fix": "prettier . --write"}, "dependencies": {"@changey/react-leaflet-markercluster": "^4.0.0-rc1", "@date-io/dayjs": "^2.16.0", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@hookform/resolvers": "^2.9.8", "@mui/icons-material": "^6.1.6", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^6.1.6", "@mui/types": "^7.2.19", "@mui/x-data-grid": "^7.27.1", "@mui/x-date-pickers": "^7.22.0", "@nivo/bar": "^0.83.0", "@nivo/core": "^0.83.0", "@nivo/heatmap": "^0.83.0", "@nivo/line": "^0.83.0", "@nivo/pie": "^0.83.0", "@nivo/stream": "^0.83.0", "@reduxjs/toolkit": "^1.8.5", "@types/leaflet": "^1.9.17", "async-mutex": "^0.4.0", "dayjs": "^1.11.6", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "mui-one-time-password-input": "^2.0.2", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.35.0", "react-leaflet": "^4.2.1", "react-leaflet-fullscreen": "^4.1.1", "react-material-ui-carousel": "^3.4.2", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "redux-persist": "^6.0.0", "screenfull": "^6.0.2", "vite-plugin-svgr": "^2.2.1", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "devDependencies": {"@redux-devtools/core": "^3.13.1", "@types/forge-viewer": "^7.69.5", "@types/jest": "^27.5.2", "@types/node": "^16.11.58", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/react-google-recaptcha": "^2.1.9", "@types/react-grid-layout": "^1.3.5", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^2.1.0", "eslint": "^8.35.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "2.8.8", "react-error-overlay": "^6.0.9", "typescript": "^4.6.4", "vite": "^3.1.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}