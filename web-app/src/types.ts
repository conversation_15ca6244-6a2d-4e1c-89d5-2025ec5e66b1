export type EnumMetaItem = {
  label: string;
  color: string;
};

/**
 * Given an enum whose values are strings or numbers,
 * produce a mapping from each enum member → its metadata.
 *
 * @example
 * enum Color { RED = "red", BLUE = "blue" }
 * const ColorMeta: EnumMetaMap<Color> = {
 *   [Color.RED]:   { label: "Red",   description: "Hot like fire" },
 *   [Color.BLUE]:  { label: "Blue",  description: "Cool like ice" },
 * }
 */
export type EnumMetaMap<E extends string> = {
  [K in E]: EnumMetaItem;
} & {
  [k: string]: EnumMetaItem | undefined;
};
