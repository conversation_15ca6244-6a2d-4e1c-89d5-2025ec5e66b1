/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Box, MenuItem, TextField, Typography, styled, useTheme } from '@mui/material';
import { FileDisplay, FileUrn } from '../../file/fileTypes';

import GuiViewer3D = Autodesk.Viewing.GuiViewer3D;
import BubbleNode = Autodesk.Viewing.BubbleNode;
import { useAppSelector } from '../../../store';

const StyledTextField = styled(TextField)(() => ({
  '& .MuiInput-underline:before': { border: 'none' },
  '& .MuiInput-underline:after': { border: 'none' },
  '& .MuiInput-root:hover:before': { border: 'none !important' },
}));

// Sprite click handling is now done inside the component with a memoized handler

interface ViewerProps {
  parentId: string;
  model?: FileDisplay | null;
  sheet: number;
  onInitialize?: (viewer: GuiViewer3D) => void;
  onSheetChange?: (newSheetNumber: number) => void;
  onClick?: (position: { x: number; y: number; z: number }) => void;
  allowSheetSelect?: boolean;
  positions: {
    position: { x: number; y: number; z: number };
    count?: number;
    locationId?: number;
    locationName?: string;
  }[];
  isModelLoading?: boolean;
}

function AutodeskViewer({
  parentId,
  model,
  sheet,
  onInitialize,
  onSheetChange,
  onClick,
  allowSheetSelect = false,
  positions,
  isModelLoading = false,
}: ViewerProps) {
  const theme = useTheme();
  const menuExpanded = useAppSelector((state) => state.toolbar.menuExpanded);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // Use a ref to track the tooltip element
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  // Use refs to track initialization status and handlers across re-renders
  const hasInitializedRef = useRef<boolean>(false);
  const timerRef = useRef<number | null>(null);
  // Store viewer element reference to use in resize handler
  const viewerElementRef = useRef<HTMLElement | null>(null);
  // Store the model URL in a ref to avoid reloading the same model
  const modelUrlRef = useRef<string | null>(null);

  // Helper: converts a 3D world coordinate to a 2D screen coordinate.
  const worldToScreen = useCallback((vector: THREE.Vector3, camera: THREE.Camera, canvas: HTMLElement) => {
    const width = canvas.clientWidth;
    const height = canvas.clientHeight;
    const vectorCopy = vector.clone();
    vectorCopy.project(camera);
    return {
      x: ((vectorCopy.x + 1) / 2) * width,
      y: ((-vectorCopy.y + 1) / 2) * height,
    };
  }, []);

  // Function to hide tooltip
  const hideTooltip = useCallback(() => {
    if (tooltipRef.current) {
      tooltipRef.current.remove();
      tooltipRef.current = null;
    }
  }, []);

  // Function to create or update tooltip
  const showTooltip = useCallback(
    (text: string, position: THREE.Vector3, viewer: GuiViewer3D) => {
      // Remove any existing tooltip
      hideTooltip();

      // Safety check - make sure viewer and container exist
      if (!viewer || !viewer.container) {
        return () => null;
      }

      // Create tooltip element
      const tooltip = document.createElement('div');
      tooltip.className = 'location-tooltip';
      tooltip.style.position = 'absolute';
      tooltip.style.backgroundColor = theme.palette.background.paper;
      tooltip.style.border = `1px solid ${theme.palette.divider}`;
      tooltip.style.borderRadius = `${theme.shape.borderRadius}px`;
      tooltip.style.boxShadow = `${theme.shadows[2]}`;
      tooltip.style.padding = '4px 8px';
      tooltip.style.fontSize = '0.875rem';
      tooltip.style.fontWeight = 'bold';
      tooltip.style.whiteSpace = 'nowrap';
      tooltip.style.zIndex = '9999';
      tooltip.style.pointerEvents = 'none';
      tooltip.textContent = text;

      // Create arrow with outline
      const arrowOutline = document.createElement('div');
      arrowOutline.style.position = 'absolute';
      arrowOutline.style.bottom = '-11px';
      arrowOutline.style.left = '50%';
      arrowOutline.style.marginLeft = '-11px';
      arrowOutline.style.borderWidth = '11px 11px 0';
      arrowOutline.style.borderStyle = 'solid';
      arrowOutline.style.borderColor = `${theme.palette.divider} transparent transparent transparent`;
      tooltip.appendChild(arrowOutline);

      // Create inner arrow
      const arrow = document.createElement('div');
      arrow.style.position = 'absolute';
      arrow.style.bottom = '-10px';
      arrow.style.left = '50%';
      arrow.style.marginLeft = '-10px';
      arrow.style.borderWidth = '10px 10px 0';
      arrow.style.borderStyle = 'solid';
      arrow.style.borderColor = `${theme.palette.background.paper} transparent transparent transparent`;
      tooltip.appendChild(arrow);

      // Add to viewer container
      viewer.container.appendChild(tooltip);

      // Store reference
      tooltipRef.current = tooltip;

      // Position tooltip
      const updatePosition = () => {
        if (!tooltipRef.current) return;
        // Safety check - make sure viewer still exists
        if (!viewer || !viewer.impl || !viewer.container) {
          hideTooltip();
          return;
        }

        const { camera } = viewer.impl;
        const canvas = viewer.container as HTMLElement;
        const screenPos = worldToScreen(position, camera, canvas);

        tooltipRef.current.style.left = `${screenPos.x}px`;
        tooltipRef.current.style.top = `${screenPos.y - 48}px`;
        tooltipRef.current.style.transform = 'translateX(-50%)';
      };

      // Initial positioning
      updatePosition();

      // Add event listener for camera change
      viewer.addEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, updatePosition);

      // Return cleanup function
      return () => {
        // Safety check before removing event listener
        if (viewer) {
          viewer.removeEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, updatePosition);
        }
        hideTooltip();
      };
    },
    [worldToScreen, hideTooltip, theme]
  );

  const [viewerInstance, setViewerInstance] = useState<GuiViewer3D>();
  const [docInstance, setDocInstance] = useState<{ doc?: Autodesk.Viewing.Document; view?: BubbleNode }>();
  const [views, setViews] = useState<BubbleNode[]>([]);
  const [viewExtension, setViewExtension] = useState<Autodesk.Extensions.DataVisualization>();
  const [geoExtension, setGeoExtension] = useState<any>();

  // State for the computed clusters.
  const [clusters, setClusters] = useState<
    {
      members: {
        position: { x: number; y: number; z: number };
        count?: number;
        locationId?: number;
        locationName?: string;
      }[];
      world: THREE.Vector3;
      screen: { x: number; y: number };
      count: number;
      locationId?: number;
      locationName?: string;
    }[]
  >([]);

  // Cache for permit data URLs to avoid recreating the same images
  const permitDataUrlCache = useRef<Map<number | undefined, string>>(new Map());

  /**
   * Creates a crisp, circular marker with a subtle gradient and white text.
   * Uses a higher-resolution "retina" canvas to keep text sharp.
   * Caches results to avoid recreating the same images.
   *
   * @param count The number of permits/items to display in the marker.
   * @returns A base64 data URL for the marker image.
   */
  const createPermitDataUrl = useCallback(
    (count?: number): string => {
      // Check if we already have this count in the cache
      if (permitDataUrlCache.current.has(count)) {
        return permitDataUrlCache.current.get(count) || '';
      }

      const displaySize = 96; // Increased to match MapViewer
      const scale = window.devicePixelRatio || 1;
      const canvas = document.createElement('canvas');
      const size = Math.round(displaySize * scale);
      canvas.width = size;
      canvas.height = size;
      canvas.style.width = `${displaySize}px`;
      canvas.style.height = `${displaySize}px`;
      const ctx = canvas.getContext('2d');
      if (!ctx) return '';
      ctx.imageSmoothingEnabled = true;
      if (ctx.imageSmoothingQuality) {
        ctx.imageSmoothingQuality = 'high';
      }
      ctx.scale(scale, scale);
      const center = displaySize / 2;
      const radius = 24; // Increased to match MapViewer
      ctx.beginPath();
      ctx.arc(center, center, radius, 0, Math.PI * 2);
      ctx.fillStyle = theme.palette.warning.main; // Use theme color
      ctx.fill();
      if (count || count === 0) {
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Quicksand, sans-serif'; // Increased to match MapViewer
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(count.toString(), center, center);
      }

      const dataUrl = canvas.toDataURL();
      // Store in cache for future use
      permitDataUrlCache.current.set(count, dataUrl);
      return dataUrl;
    },
    [theme]
  ); // Add theme to dependencies

  // Clustering algorithm: groups markers that are within `threshold` pixels on screen.
  // Memoized to avoid recreating the function on every render
  const computeClusters = useCallback(
    (
      markerPositions: {
        position: { x: number; y: number; z: number };
        count?: number;
        locationId?: number;
        locationName?: string;
      }[],
      viewer: GuiViewer3D,
      threshold = 50,
      geoExt?: any
    ) => {
      // Early return for empty positions
      if (!markerPositions.length) return [];

      const clusterList: {
        members: {
          position: { x: number; y: number; z: number };
          count?: number;
          locationId?: number;
          locationName?: string;
        }[];
        world: THREE.Vector3;
        screen: { x: number; y: number };
        count: number;
        locationId?: number;
        locationName?: string;
      }[] = [];
      const { camera } = viewer.impl;
      const canvas = viewer.container as HTMLElement;

      // Pre-calculate world vectors for all positions to avoid redundant calculations
      const worldVectors = markerPositions.map((p) => {
        let worldVector = new THREE.Vector3(p.position.x, p.position.y, p.position.z);
        if (geoExt) {
          try {
            const geoWorldVector = geoExt.lonLatToLmv({ x: worldVector.x, y: worldVector.y, z: worldVector.z });
            worldVector = new THREE.Vector3(geoWorldVector.x, geoWorldVector.y, geoWorldVector.z);
          } catch (err) {
            // Silently handle any geolocation errors
          }
        }
        return { position: p, worldVector, screenPos: worldToScreen(worldVector, camera, canvas) };
      });

      // Process each position for clustering
      worldVectors.forEach(({ position: p, worldVector, screenPos }) => {
        // Use .find() to locate an existing cluster within the threshold distance.
        // Using squared distance to avoid expensive sqrt operations
        const existingCluster = clusterList.find((cluster) => {
          const dx = screenPos.x - cluster.screen.x;
          const dy = screenPos.y - cluster.screen.y;
          return dx * dx + dy * dy < threshold * threshold;
        });

        if (existingCluster) {
          existingCluster.members.push(p);
          // Recalculate cluster center and count
          let sumX = 0;
          let sumY = 0;
          let sumZ = 0;
          let sumScreenX = 0;
          let sumScreenY = 0;
          let totalCount = 0;

          existingCluster.members.forEach((member) => {
            const memberWorld = new THREE.Vector3(member.position.x, member.position.y, member.position.z);
            sumX += memberWorld.x;
            sumY += memberWorld.y;
            sumZ += memberWorld.z;
            const s = worldToScreen(memberWorld, camera, canvas);
            sumScreenX += s.x;
            sumScreenY += s.y;
            totalCount += member.count || 0; // Simplified null check
          });

          const n = existingCluster.members.length;
          existingCluster.world = new THREE.Vector3(sumX / n, sumY / n, sumZ / n);
          existingCluster.screen = { x: sumScreenX / n, y: sumScreenY / n };
          existingCluster.count = totalCount;
        } else {
          clusterList.push({
            members: [p],
            world: worldVector,
            screen: screenPos,
            count: p.count || 0, // Simplified null check
            locationId: p.locationId,
            locationName: p.locationName,
          });
        }
      });

      return clusterList;
    },
    [worldToScreen] // Only depends on worldToScreen function
  );

  // Memoize the convertBase64UrnToFileUrn function
  const convertBase64UrnToFileUrnMemo = useCallback((base64Urn: string): FileUrn => {
    const decodedString = atob(base64Urn);
    const lastColonIndex = decodedString.lastIndexOf(':');
    const decodedUrnId = decodedString.substring(0, lastColonIndex);
    const accessToken = decodedString.substring(lastColonIndex + 1);
    return {
      urnId: btoa(decodedUrnId),
      accessToken,
    };
  }, []);

  /**
   * Setup viewer instance and load document by URN.
   * Uses a ref to prevent double initialization in React StrictMode
   */
  useEffect(() => {
    // Set loading state when we have a model but no viewer instance yet
    if (model && model !== null && !viewerInstance) {
      setIsLoading(true);
    }

    // Only initialize if we don't have a viewer instance, have a model, and haven't initialized before
    if (!viewerInstance && model && model !== null && !hasInitializedRef.current) {
      // Set the ref to true to prevent double initialization
      hasInitializedRef.current = true;

      const fileUrn = convertBase64UrnToFileUrnMemo(model.url);
      const options = {
        env: 'AutodeskProduction',
        api: 'derivativeV2',
        getAccessToken(onGetAccessToken: (token: string, expireAfter: number) => void) {
          const expiresIn = 3600;
          onGetAccessToken(fileUrn.accessToken, expiresIn);
        },
      };

      Autodesk.Viewing.Initializer(options, () => {
        const parent = document.getElementById(parentId) as HTMLElement;
        const htmlDiv = document.getElementById('viewerContainer') as HTMLElement;

        if (!parent || !htmlDiv) {
          // eslint-disable-next-line no-console
          console.error('Required DOM elements not found');
          setIsLoading(false);
          return;
        }

        const viewer = new Autodesk.Viewing.GuiViewer3D(htmlDiv);
        viewer.setTheme('light-theme');
        const element = viewer.container as HTMLElement;
        element.style.height = `${parent.offsetHeight}px`;
        element.style.width = `${parent.offsetWidth}px`;

        // Store the viewer element in a ref for the global resize handler
        viewerElementRef.current = element;

        viewer.start();
        viewer.setBackgroundColor(255, 255, 255, 255, 255, 255);
        viewer.setReverseZoomDirection(true);

        // NOTE: We no longer attach the click event here.
        setViewerInstance(viewer);
        if (onInitialize) {
          onInitialize(viewer);
        }
      });
    } else if (!model || model === null) {
      // Reset loading state if there's no model
      setIsLoading(false);
    }

    // No cleanup needed for this effect as we're handling resize separately
  }, [model, parentId, onInitialize, convertBase64UrnToFileUrnMemo, viewerInstance]);

  // Define the click handler first
  const handleClick = useCallback(
    (e: MouseEvent & { hasStopped?: boolean }, viewer: GuiViewer3D, geoExt: any) => {
      if (!onClick) return;

      // Check if this is a sprite click - if so, don't process it
      if (e.hasStopped) {
        return;
      }

      const rect = viewer.canvas.getBoundingClientRect();
      const xClient = e.clientX - rect.left;
      const yClient = e.clientY - rect.top;
      const result = viewer.clientToWorld(xClient, yClient);

      if (result) {
        const { point } = result;
        try {
          // Ensure model and its data are available.
          if (geoExt.hasGeolocationData()) {
            const geoCoord = geoExt.lmvToLonLat(point);
            onClick({ x: geoCoord.x, y: geoCoord.y, z: geoCoord.z });
          } else {
            onClick({ x: point.x, y: point.y, z: point.z });
          }
        } catch (err) {
          onClick({ x: point.x, y: point.y, z: point.z });
        }
      }
    },
    [onClick]
  );

  // 1. Initial model loading effect - only runs once when model changes
  useEffect(() => {
    if (viewerInstance && model && model !== null) {
      const fileUrn = convertBase64UrnToFileUrnMemo(model.url);
      const currentModelUrl = `urn:${fileUrn.urnId}`;

      // Only reload if the model URL has changed
      if (modelUrlRef.current !== currentModelUrl) {
        modelUrlRef.current = currentModelUrl;
        setIsLoading(true); // Set loading state when starting to load document

        Autodesk.Viewing.Document.load(
          currentModelUrl,
          (doc: Autodesk.Viewing.Document) => {
            const viewables = doc.getRoot().search({ type: 'geometry' });
            if (viewables.length === 0) {
              setError('No viewable content found in the model');
              setIsLoading(false); // Reset loading state on error
              return;
            }

            // Make sure sheet index is valid
            const validSheetIndex = Math.min(sheet, viewables.length - 1);
            const view = viewables[validSheetIndex];

            setDocInstance({ doc, view });
            setViews(viewables);
            setError('');
            // We'll set loading to false after the document node is loaded in the next effect
          },
          () => {
            setError('Something went wrong on our servers while loading the model');
            setIsLoading(false); // Reset loading state on error
          }
        );
      }
    } else if (viewerInstance && (!model || model == null)) {
      // Clean up viewer resources
      viewerInstance.tearDown();
      viewerInstance.finish();

      // Hide any existing tooltip
      hideTooltip();

      // Reset state
      setViewerInstance(undefined);
      setDocInstance(undefined);
      setViewExtension(undefined);
      setGeoExtension(undefined);
      setClusters([]);
      modelUrlRef.current = null;
      setIsLoading(false); // Reset loading state when cleaning up

      // Reset initialization flag when viewer is destroyed
      hasInitializedRef.current = false;
    }
  }, [sheet, model, viewerInstance, convertBase64UrnToFileUrnMemo, hideTooltip]);

  // 2. Separate effect for loading document node and extensions
  useEffect(() => {
    if (!viewerInstance || !docInstance?.doc || !docInstance?.view) return;

    viewerInstance.setBackgroundColor(255, 255, 255, 255, 255, 255);
    viewerInstance
      .loadDocumentNode(docInstance.doc, docInstance.view)
      .then(() => {
        viewerInstance.setBackgroundColor(255, 255, 255, 255, 255, 255);
        // Ensure default shading is used
        viewerInstance.setEnvMapBackground(true);

        // Load extensions
        Promise.all([
          viewerInstance.loadExtension('Autodesk.DataVisualization'),
          viewerInstance.loadExtension('Autodesk.Geolocation'),
        ])
          .then(([dataVizExt, geoExt]) => {
            setViewExtension(dataVizExt as Autodesk.Extensions.DataVisualization);
            setGeoExtension(geoExt);
            setIsLoading(false); // Model and extensions are fully loaded
          })
          .catch(() => {
            setIsLoading(false); // Reset loading state even if extensions fail to load
          });
      })
      .catch(() => {
        setIsLoading(false); // Reset loading state if document node loading fails
      });
  }, [viewerInstance, docInstance]);

  // 3. Effect to handle sheet changes
  useEffect(() => {
    if (!viewerInstance || !docInstance?.doc || views.length === 0) return;

    // Make sure sheet index is valid
    const validSheetIndex = Math.min(sheet, views.length - 1);
    const newView = views[validSheetIndex];

    // Only reload if the view has changed
    if (docInstance.view && newView.data.viewableID !== docInstance.view.data.viewableID) {
      // Update the docInstance with the new view
      setDocInstance({ doc: docInstance.doc, view: newView });

      // Load the new document node
      setIsLoading(true);
      viewerInstance
        .loadDocumentNode(docInstance.doc, newView)
        .then(() => {
          viewerInstance.setBackgroundColor(255, 255, 255, 255, 255, 255);

          // If we already have extensions loaded, we don't need to reload them
          if (viewExtension && geoExtension) {
            setIsLoading(false);
          } else {
            // Load extensions if they're not already loaded
            Promise.all([
              viewerInstance.loadExtension('Autodesk.DataVisualization'),
              viewerInstance.loadExtension('Autodesk.Geolocation'),
            ])
              .then(([dataVizExt, geoExt]) => {
                setViewExtension(dataVizExt as Autodesk.Extensions.DataVisualization);
                setGeoExtension(geoExt);
                setIsLoading(false);
              })
              .catch(() => {
                setIsLoading(false);
              });
          }
        })
        .catch(() => {
          setIsLoading(false);
        });
    }
  }, [sheet, viewerInstance, docInstance, views, viewExtension, geoExtension]);

  // 4. Separate effect for click handler setup
  useEffect(() => {
    if (!viewerInstance || !geoExtension || !onClick) return () => null;

    // Safety check - make sure viewer container exists
    if (!viewerInstance.container) {
      return () => null;
    }

    // Handle regular clicks on the model (not on sprites)
    const clickWrapper = (e: MouseEvent & { hasStopped?: boolean }) => {
      // Only process clicks that haven't been stopped by sprite click handler
      if (!e.hasStopped) {
        // Process the click for position
        handleClick(e, viewerInstance, geoExtension);
      }
    };

    viewerInstance.container.addEventListener('click', clickWrapper);

    return () => {
      if (viewerInstance?.container) {
        viewerInstance.container.removeEventListener('click', clickWrapper);
      }
    };
  }, [viewerInstance, geoExtension, handleClick, onClick]);

  // 5. Position update effect with debounced clustering
  const createDebouncedUpdateClusters = useCallback(
    () => () => {
      if (timerRef.current !== null) {
        clearTimeout(timerRef.current);
      }
      timerRef.current = window.setTimeout(() => {
        if (viewerInstance && geoExtension) {
          // If positions is empty, immediately clear clusters
          if (positions.length === 0) {
            setClusters([]);
          } else {
            const newClusters = computeClusters(positions, viewerInstance, 32, geoExtension);
            setClusters(newClusters);
          }
        }
      }, 300);
    },
    [positions, viewerInstance, geoExtension, computeClusters]
  );

  useEffect(() => {
    // Clear clusters when positions become empty
    if (viewerInstance && positions.length === 0) {
      // Clear clusters immediately when positions are empty
      setClusters([]);
      return () => null;
    }

    if (viewerInstance && geoExtension) {
      const debouncedUpdateClusters = createDebouncedUpdateClusters();
      debouncedUpdateClusters();

      // Safety check - make sure viewer container exists
      if (!viewerInstance.container) {
        return () => null;
      }

      // Listen for camera changes
      viewerInstance.addEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, debouncedUpdateClusters);

      return () => {
        if (viewerInstance) {
          viewerInstance.removeEventListener(Autodesk.Viewing.CAMERA_CHANGE_EVENT, debouncedUpdateClusters);
        }
        if (timerRef.current !== null) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      };
    }
    return () => null;
  }, [positions, viewerInstance, geoExtension, createDebouncedUpdateClusters]);

  // Memoize the sprite click handler to prevent recreating it on every render
  const memoizedSpriteClickHandler = useCallback(
    (event: {
      hasStopped: boolean;
      target?: any;
      stopPropagation?: () => void;
      preventDefault?: () => void;
      dbId?: number;
      type?: string;
    }) => {
      // Only process click events
      if (event.type !== Autodesk.DataVisualization.Core.MOUSE_CLICK) {
        return true;
      }

      // Stop the event propagation to prevent the model from reloading
      // eslint-disable-next-line no-param-reassign
      event.hasStopped = true;

      // Prevent the default click behavior
      if (event.preventDefault) {
        event.preventDefault();
      }

      // Stop propagation to prevent the regular click handler from being triggered
      if (event.stopPropagation) {
        event.stopPropagation();
      }

      // Find the sprite viewable that corresponds to this dbId
      if (event.dbId && viewExtension) {
        // Get the viewable data from the extension - use type assertion for TypeScript
        // Not using object destructuring to avoid linting issues with TypeScript
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, prefer-destructuring
        const viewableData = (viewExtension as any).viewableData;

        if (viewableData) {
          // Find the viewable with the matching dbId
          // eslint-disable-next-line @typescript-eslint/no-explicit-any, prefer-destructuring
          const viewables = viewableData.viewables;
          const viewable = viewables.find((v: any) => v.dbId === event.dbId);

          if (viewable && (viewable as any).myContextData) {
            const contextData = (viewable as any).myContextData;
            const { locationName, locationId } = contextData;

            // Show tooltip using our direct DOM approach
            if (viewerInstance) {
              try {
                // Create or update tooltip
                showTooltip(locationName || 'Location', viewable.position, viewerInstance);
              } catch (err) {
                // Silent error handling
              }
            }

            // Dispatch a custom event that can be listened to by parent components
            viewerInstance?.container.dispatchEvent(
              new CustomEvent('sprite-click', {
                detail: {
                  locationName: locationName || 'Unknown',
                  locationId,
                  position: contextData.position,
                },
              })
            );
          }
        }
      }

      // We don't want to trigger the regular click handler for sprites
      // as that would cause the model to reload
      return true;
    },
    [viewerInstance, viewExtension, showTooltip]
  );

  /**
   * Update the DataVisualization extension with clustered work permit sprites.
   */
  useEffect(() => {
    if (!viewExtension || !viewerInstance) return undefined;

    // Safety check - make sure viewer container exists
    if (!viewerInstance.container) {
      return undefined;
    }

    // Always clear previous viewables first
    viewExtension.clearHighlightedViewables();
    viewExtension.removeAllViewables();

    // Skip further update if clusters is empty
    if (clusters.length === 0) {
      return undefined;
    }

    const { Core } = Autodesk.DataVisualization;
    const viewableData = new Core.ViewableData();
    viewableData.spriteSize = 48;
    const viewableType = Core.ViewableType.SPRITE;

    // Create all viewables in a single batch
    clusters.forEach((cluster, index) => {
      const dbId = 10000 + index;
      const permitDataUrl = createPermitDataUrl(cluster.count);
      const style = new Core.ViewableStyle(
        viewableType,
        new THREE.Color(0xffffff),
        permitDataUrl,
        new THREE.Color(0xffffff),
        permitDataUrl
      );
      const viewable = new Core.SpriteViewable(cluster.world, style, dbId);

      // Tag custom data on this instance of SpriteViewable for later use
      // Use type assertion to avoid TypeScript error
      (viewable as any).myContextData = {
        locationId: cluster.locationId,
        locationName: cluster.locationName,
        count: cluster.count,
        position: {
          x: cluster.world.x,
          y: cluster.world.y,
          z: cluster.world.z,
        },
      };

      viewableData.addViewable(viewable);
    });

    // Add click handlers - use our memoized handlers
    viewerInstance.addEventListener(Core.MOUSE_CLICK, memoizedSpriteClickHandler);

    // Add MOUSE_CLICK_OUT handler to hide tooltip when clicking outside sprites
    const handleClickOut = (event: any) => {
      // Mark the event as stopped to prevent further propagation
      // eslint-disable-next-line no-param-reassign
      event.hasStopped = true;

      // Hide the tooltip when clicking outside of sprites
      hideTooltip();
    };

    viewerInstance.addEventListener(Core.MOUSE_CLICK_OUT, handleClickOut);

    // Finish and add viewables
    viewableData.finish().then(() => {
      if (viewExtension) {
        // Check again in case it was unmounted
        viewExtension.addViewables(viewableData);
      }
    });

    // Return cleanup function
    return () => {
      if (viewerInstance) {
        viewerInstance.removeEventListener(Core.MOUSE_CLICK, memoizedSpriteClickHandler);
        viewerInstance.removeEventListener(Core.MOUSE_CLICK_OUT, handleClickOut);
      }
    };
  }, [clusters, viewExtension, viewerInstance, createPermitDataUrl, memoizedSpriteClickHandler, hideTooltip]);

  // Create a resize handler that will work across component remounts
  const handleResize = useCallback(() => {
    if (!viewerInstance || !viewerElementRef.current) return;

    const element = viewerElementRef.current;
    const newParent = document.getElementById(parentId) as HTMLElement;

    if (element && element.style && newParent) {
      element.style.height = `${newParent.offsetHeight}px`;
      element.style.width = `${newParent.offsetWidth}px`;
      viewerInstance.resize();
      // Only use full refresh when absolutely necessary as it's expensive
      viewerInstance.refresh(false);
    }
  }, [viewerInstance, parentId]);

  // Set up the resize handler
  useEffect(() => {
    // Add the resize listener
    window.addEventListener('resize', handleResize);

    // Call once to ensure proper initial sizing
    handleResize();

    // Cleanup function to remove event listener when component unmounts
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [handleResize]);

  // Handle viewer resizing when relevant props change (like menu expansion)
  useEffect(() => {
    handleResize();
  }, [handleResize, menuExpanded]); // Only depend on props that affect size

  // Memoize the styles to prevent unnecessary re-renders
  const boxStyles = useMemo(
    () => ({
      '.adsk-viewing-viewer': {
        borderRadius: 1,
        fontFamily: `${theme.typography.fontFamily} !important`,
      },
    }),
    [theme.typography.fontFamily]
  );

  return (
    <Box height="100%" width="100%" sx={boxStyles}>
      {/* Using direct DOM approach for tooltip now */}

      {error.length > 0 && (
        <Box display="flex" height="100%" alignItems="center">
          <Typography variant="h5" textAlign="center" mb={16} width="100%">
            Vinkey was unable to load the model
          </Typography>
        </Box>
      )}
      {!viewerInstance && !isLoading && !isModelLoading && (
        <Box display="flex" height="100%" alignItems="center">
          <Typography variant="h5" textAlign="center" mb={16} width="100%">
            No model configured
          </Typography>
        </Box>
      )}
      {(!viewerInstance && isLoading) || isModelLoading ? (
        <Box display="flex" height="100%" alignItems="center">
          <Typography variant="h5" textAlign="center" mb={16} width="100%">
            Loading model...
          </Typography>
        </Box>
      ) : null}
      <Box id="viewerContainer" />
      {viewerInstance && allowSheetSelect && docInstance?.view && (
        <StyledTextField
          sx={{
            position: 'absolute',
            zIndex: 2,
            '& .MuiInputBase-input': {
              px: 1,
              py: 0.5,
              borderBottomRightRadius: '4px !important',
              borderTopLeftRadius: '4px !important',
            },
          }}
          onChange={(e) => {
            const index = views.findIndex((v) => v.data.viewableID === e.target.value);
            if (index > -1 && onSheetChange) {
              onSheetChange(index);
            }
          }}
          variant="standard"
          value={docInstance?.view.data.viewableID}
          placeholder="No sheet selected"
          size="small"
          autoComplete="off"
          select
        >
          {views.map((v) => (
            <MenuItem key={v.data.viewableID} value={v.data.viewableID}>
              {v.data.name}
            </MenuItem>
          ))}
        </StyledTextField>
      )}
    </Box>
  );
}

export default AutodeskViewer;
