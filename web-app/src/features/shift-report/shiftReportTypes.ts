import { Dayjs } from 'dayjs';
import { GroupDisplay } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { ShiftLogPriority, ShiftLogRead } from './log/shiftLogTypes';
import { SubTopicRead } from './sub-topic/subTopicTypes';
import { Topic } from './topic/topicTypes';
import { ShiftRead } from './shift/shiftTypes';
import { EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';

export interface ShiftReportParams {
  groupId?: number;
  createdBy?: number;
  ancestorGroupId?: number;
  startDateLte?: number;
  startDateGte?: number;
  endDateLte?: number;
  endDateGte?: number;
  status?: ShiftReportStatus;
  candidateGroups?: ShiftReportCandidatGroup[];
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
  search?: string;
}

export interface ShiftReportNeighbourParams {
  id: number;
  groupId?: number;
  createdBy?: number;
  ancestorGroupId?: number;
  startDateLte?: number;
  startDateGte?: number;
  endDateLte?: number;
  endDateGte?: number;
  status?: ShiftReportStatus;
  candidateGroups?: ShiftReportCandidatGroup[];
  pageSize?: number;
  pageNumber?: number;
  search?: string;
  filter?: string;
}

export interface ShiftReportFormInput {
  group: GroupDisplay | null;
  shift: ShiftRead | null;
  startDate: Dayjs | null;
  startTime: Dayjs | null;
  endTime: Dayjs | null;
  endDate: Dayjs | null;
  topics: Topic[] | [];
  status?: ShiftReportStatus;
}

export interface ShiftReportPDFParams {
  id: number;
  timeZone: string;
}

export enum ShiftReportCandidatGroup {
  SHIFT_REPORT_START_HANDOVER = 'SHIFT_REPORT_START_HANDOVER',
  SHIFT_REPORT_ACCEPT_HANDOVER = 'SHIFT_REPORT_ACCEPT_HANDOVER',
}

export const ShiftReportCandidateGroupDisplayMap: Record<ShiftReportCandidatGroup, string> = {
  SHIFT_REPORT_START_HANDOVER: 'Start handover',
  SHIFT_REPORT_ACCEPT_HANDOVER: 'Accept handover',
};

export interface ShiftReportViewState {
  listView: 'mine' | 'all';
  group?: GroupDisplay;
  status?: ShiftReportStatus;
  candidateGroups?: ShiftReportCandidatGroup[];
  createdBy?: User;
  search?: string;
  startDateGte?: number;
  endDateLte?: number;
}
export interface ShiftReportState {
  shiftReportViewState: ShiftReportViewState;
}

export enum ShiftReportStatus {
  OPEN = 'OPEN',
  HANDOVER_STARTED = 'HANDOVER_STARTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const ShiftReportStatusMeta: EnumMetaMap<ShiftReportStatus> = {
  [ShiftReportStatus.OPEN]: { label: 'Open', color: themeToColor('primary.main') },
  [ShiftReportStatus.HANDOVER_STARTED]: { label: 'Handover started', color: themeToColor('secondary.main') },
  [ShiftReportStatus.CLOSED]: { label: 'Closed', color: themeToColor('success.main') },
  [ShiftReportStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const ShiftReportStatusDisplayMap = Object.fromEntries(
  Object.entries(ShiftReportStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<ShiftReportStatus, string>;

export interface ShiftReportSubTopic {
  id: number;
  subTopic: SubTopicRead;
  logs: ShiftLogRead[];
}

export interface ShiftReportRead {
  id: number;
  sid: number;
  processInstanceId: string;
  locked: boolean;
  startTime: number;
  endTime: number;
  group: GroupDisplay;
  status: ShiftReportStatus;
  subTopics: ShiftReportSubTopic[];
  createdBy: User;
  creationDate: number;
  modifiedBy: User;
  modifiedDate: number;
}

export interface ShiftReportListRead {
  id: number;
  sid: number;
  startTime: number;
  endTime: number;
  group: GroupDisplay;
  createdBy: UserDisplay;
  status: ShiftReportStatus;
}

export interface ShiftReportCreate {
  startTime: number;
  endTime: number;
  group: number;
}

export interface ShiftReportUpdate {
  id: number;
  startTime: number;
  endTime: number;
  subTopics: ShiftReportSubTopicUpdate[];
}

export interface ShiftReportSubTopicUpdate {
  id: number;
  subTopic: number;
  logs: ShiftLogUpdate[];
}

export interface ShiftLogUpdate {
  id?: number;
  sid?: number;
  description: string;
  referenceNumber?: string;
  priority: ShiftLogPriority;
}

export interface ShiftReportDeletable {
  deletable: boolean;
}

export enum ShiftReportChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}
export const ShiftReportChangeTypeDisplayMap: Record<ShiftReportChangeType, string> = {
  INSERT: 'Shift report created',
  UPDATE: 'Shift report updated',
  DELETE: 'Shift report deleted',
};
export interface ShiftReportChange {
  by: UserDisplay;
  at: number;
  type: ShiftReportChangeType;
  oldEntity: ShiftReportRead;
  newEntity: ShiftReportRead;
}

export interface NeighbourResult {
  current: number;
  next: number;
  previous: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export enum ShiftReportGroupBy {
  GROUP = 'SHIFT_REPORTS_GROUP',
  CREATION_DATE_WEEK = 'SHIFT_REPORTS_CREATION_DATE_WEEK',
  CREATED_BY = 'SHIFT_REPORTS_CREATED_BY',
  STATUS = 'SHIFT_REPORTS_STATUS',
}

export const ShiftReportGroupByDisplayMap: Record<ShiftReportGroupBy, string> = {
  [ShiftReportGroupBy.GROUP]: 'Group',
  [ShiftReportGroupBy.CREATED_BY]: 'User',
  [ShiftReportGroupBy.STATUS]: 'Status',
  [ShiftReportGroupBy.CREATION_DATE_WEEK]: 'Shift report week',
};

export interface ShiftReportGroupByFieldType {
  [ShiftReportGroupBy.STATUS]: ShiftReportStatus;
}

export const ShiftReportGroupByFieldMetaMap: {
  [K in keyof ShiftReportGroupByFieldType]: EnumMetaMap<ShiftReportGroupByFieldType[K]>;
} = {
  [ShiftReportGroupBy.STATUS]: ShiftReportStatusMeta,
};
