import dayjs from 'dayjs';
import { CountObject, KeyTotal, StatsKeyValue, TimeSeriesDataPoint, WeekTotalCount } from './statsTypes';

export function statsToTimeSeries(data: StatsKeyValue): TimeSeriesDataPoint[] {
  return Object.entries(data).flatMap(([date, value]): TimeSeriesDataPoint[] => {
    if ('count' in value) {
      return [{ date, total: (value as CountObject).count }];
    }

    const nested = value as StatsKeyValue;
    const keyTotals: KeyTotal[] = Object.entries(nested)
      .filter(([, v]) => 'count' in v)
      .map(([key, v]) => ({
        key,
        total: (v as CountObject).count,
      }));

    return [{ date, total: keyTotals }];
  });
}

export function generateCompleteTimeSeries(
  newData: TimeSeriesDataPoint[],
  startDate: number,
  endDate: number
): WeekTotalCount[] {
  const dataMap: Record<string, TimeSeriesDataPoint> = {};

  newData.forEach((item) => {
    const dateObj = dayjs(item.date);
    const week = dateObj.isoWeek();
    const year = dateObj.year();
    dataMap[`${year}-${week}`] = item;
  });

  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const completeSeries: WeekTotalCount[] = [];

  let current = start.startOf('isoWeek');
  while (current.isBefore(end) || current.isSame(end, 'day')) {
    const year = current.year();
    const week = current.isoWeek();
    const date = `${year}-${week}`;

    completeSeries.push({
      week,
      year,
      total: dataMap[date]?.total || 0,
    });

    current = current.add(1, 'week');
  }

  return completeSeries;
}

/**
 * Extracts all numerical count values from statsData, assuming at most two levels:
 *  • statsData[row].count
 *  • statsData[row][col].count
 * @param statsData – an object whose values are either { count: number } or an object mapping to { count: number }
 * @returns a flat array of every count found (skips anything that isn’t a number)
 */
export function getValuesFromStatsData(statsData: StatsKeyValue): number[] {
  const values: number[] = [];

  Object.values(statsData).forEach((entry) => {
    if (entry && typeof entry === 'object' && 'count' in entry && typeof entry.count === 'number') {
      values.push(entry.count);
    } else if (entry && typeof entry === 'object') {
      Object.values(entry).forEach((subEntry) => {
        if (subEntry && typeof subEntry === 'object' && 'count' in subEntry && typeof subEntry.count === 'number') {
          values.push(subEntry.count);
        }
      });
    }
  });

  return values.filter((v) => !Number.isNaN(v));
}

/**
 * Finds the maximum count value in the stats data structure
 * @param statsData - an object whose values are either { count: number } or an object mapping to { count: number }
 * @returns the highest count value found, or 0 if no values exist
 */
export function getMaxValueFromStatsData(statsData: StatsKeyValue): number {
  const values = getValuesFromStatsData(statsData);
  return values.length > 0 ? Math.max(...values) : 0;
}
