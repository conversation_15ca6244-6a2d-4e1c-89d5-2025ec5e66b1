import { useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
} from '@mui/material';
import { StatsKeyValue, StatsGroupBy } from './statsTypes';
import { getFieldName } from '../chart/chartFunctions';

interface PivotTableProps {
  data: StatsKeyValue;
  fieldGroupByNamingMapping: Record<string, string>;
  fieldStackedByNamingMapping: Record<string, string>;
  groupBy: StatsGroupBy;
  stackedBy: StatsGroupBy;
}

interface PivotData {
  rows: string[];
  columns: string[];
  values: Record<string, Record<string, number>>;
  totals: {
    rowTotals: Record<string, number>;
    columnTotals: Record<string, number>;
    grandTotal: number;
  };
}

function StatsPivotChart({
  data,
  fieldGroupByNamingMapping,
  fieldStackedByNamingMapping,
  groupBy,
  stackedBy,
}: PivotTableProps) {
  const pivotData = useMemo((): PivotData => {
    const rows = new Set<string>();
    const columns = new Set<string>();
    const values: Record<string, Record<string, number>> = {};
    const rowTotals: Record<string, number> = {};
    const columnTotals: Record<string, number> = {};
    let grandTotal = 0;

    Object.entries(data).forEach(([rowKey, rowValue]) => {
      if (typeof rowValue === 'object' && !('count' in rowValue)) {
        rows.add(rowKey);
        if (!values[rowKey]) values[rowKey] = {};
        if (!rowTotals[rowKey]) rowTotals[rowKey] = 0;

        Object.entries(rowValue).forEach(([colKey, colValue]) => {
          if (typeof colValue === 'object' && 'count' in colValue) {
            columns.add(colKey);
            const { count } = colValue as { count: number };
            values[rowKey][colKey] = count;
            rowTotals[rowKey] += count;
            columnTotals[colKey] = (columnTotals[colKey] || 0) + count;
            grandTotal += count;
          }
        });
      }
    });

    return {
      rows: Array.from(rows).sort(),
      columns: Array.from(columns).sort(),
      values,
      totals: { rowTotals, columnTotals, grandTotal },
    };
  }, [data]);

  const getDisplayName = (key: string, type: 'row' | 'column') => {
    if (type === 'row') {
      return fieldGroupByNamingMapping[key] || getFieldName(key, groupBy) || key;
    }
    return fieldStackedByNamingMapping[key] || getFieldName(key, stackedBy) || key;
  };

  if (pivotData.rows.length === 0 || pivotData.columns.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="text.secondary">No data available for pivot table</Typography>
      </Box>
    );
  }

  return (
    <TableContainer component={Paper} sx={{ height: '100%', overflow: 'auto' }}>
      <Table stickyHeader size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.100' }}>{/* Empty corner cell */}</TableCell>
            {pivotData.columns.map((col) => (
              <TableCell
                key={col}
                align="right"
                sx={{ fontWeight: 'bold', backgroundColor: 'grey.100', minWidth: 100 }}
              >
                {getDisplayName(col, 'column')}
              </TableCell>
            ))}
            <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.200', minWidth: 100 }}>
              Total
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {pivotData.rows.map((row) => (
            <TableRow key={row} hover>
              <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                {getDisplayName(row, 'row')}
              </TableCell>
              {pivotData.columns.map((col) => (
                <TableCell key={col} align="right">
                  {pivotData.values[row]?.[col] || 0}
                </TableCell>
              ))}
              <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                {pivotData.totals.rowTotals[row] || 0}
              </TableCell>
            </TableRow>
          ))}
          <TableRow sx={{ backgroundColor: 'grey.100' }}>
            <TableCell sx={{ fontWeight: 'bold' }}>Total</TableCell>
            {pivotData.columns.map((col) => (
              <TableCell key={col} align="right" sx={{ fontWeight: 'bold' }}>
                {pivotData.totals.columnTotals[col] || 0}
              </TableCell>
            ))}
            <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.200' }}>
              {pivotData.totals.grandTotal}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default StatsPivotChart;
