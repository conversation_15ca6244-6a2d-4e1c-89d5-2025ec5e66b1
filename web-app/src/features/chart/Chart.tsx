import React, { useState, useRef, useLayoutEffect, useMemo } from 'react';
import { Box, Typography, IconButton, Paper, CircularProgress, Menu, MenuItem } from '@mui/material';
import SettingsIcon from '@mui/icons-material/SettingsOutlined';
import DeleteIcon from '@mui/icons-material/DeleteOutlined';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import dayjs from 'dayjs';
import { useGetStatsQuery } from '../stats/statsApi';
import { StatsGroupBy } from '../stats/statsTypes';
import getDateRangeFromRollingPeriod, { getFieldColor, getFieldName } from './chartFunctions';
import { ChartSetting, ChartType } from './chartTypes';
import BarChart from './components/BarChart';
import LineChart from './components/LineChart';
import Pie<PERSON>hart from './components/PieChart';
import PivotTable from './components/PivotTable';

interface ChartProps {
  groupId: number;
  chartSetting: ChartSetting;
  index: number;
  editMode: boolean;
  onEditChart: (index: number) => void;
  onDeleteChart: (index: number) => void;
}

function Chart({ groupId, chartSetting, index, editMode, onEditChart, onDeleteChart }: ChartProps) {
  const {
    groupBy,
    fieldColorMapping,
    type: chartType,
    module: moduleType,
    rollingPeriod,
    stackedBy,
    name,
    id,
    orientation,
    valueColorRanges,
  } = chartSetting;
  const { startDate, endDate } = getDateRangeFromRollingPeriod(rollingPeriod);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [headerHeight, setHeaderHeight] = useState(0);

  const headerRef = useRef<HTMLDivElement>(null);
  const open = Boolean(anchorEl);

  const groupByParams = useMemo(() => [groupBy, stackedBy].filter(Boolean) as StatsGroupBy[], [groupBy, stackedBy]);

  const { data: statsData, isLoading } = useGetStatsQuery({
    ancestorGroupId: groupId,
    startDate,
    endDate,
    module: moduleType,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    groupBy: groupByParams,
  });

  const groupByColorKeys = useMemo(() => {
    if (!statsData || !groupBy) return [];
    return chartType === ChartType.LINE ? ['total'] : Object.keys(statsData);
  }, [statsData, chartType, groupBy]);

  const stackedByColorKeys = useMemo(() => {
    if (!statsData || !stackedBy) return [];
    return Object.values(statsData)
      .filter((v) => typeof v === 'object' && !('count' in v))
      .flatMap((v) => Object.keys(v));
  }, [statsData, stackedBy]);

  const fieldGroupByColorMapping = useMemo(
    () =>
      groupByColorKeys.reduce((acc, key) => {
        acc[key] = getFieldColor(key, fieldColorMapping, groupBy);
        return acc;
      }, {} as Record<string, string>),
    [groupByColorKeys, fieldColorMapping, groupBy]
  );

  const fieldStackedByColorMapping = useMemo(
    () =>
      stackedByColorKeys.reduce((acc, key) => {
        acc[key] = getFieldColor(key, fieldColorMapping, stackedBy);
        return acc;
      }, {} as Record<string, string>),
    [stackedByColorKeys, fieldColorMapping, stackedBy]
  );

  const fieldGroupByNamingMapping = useMemo(
    () =>
      groupByColorKeys.reduce((acc, key) => {
        acc[key] = getFieldName(key, groupBy);
        return acc;
      }, {} as Record<string, string>),
    [groupByColorKeys, groupBy]
  );

  const fieldStackedByNamingMapping = useMemo(
    () =>
      stackedByColorKeys.reduce((acc, key) => {
        acc[key] = getFieldName(key, stackedBy);
        return acc;
      }, {} as Record<string, string>),
    [stackedByColorKeys, stackedBy]
  );

  useLayoutEffect(() => {
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.offsetHeight);
    }
  }, [name, editMode]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const renderChartHeader = () => (
    <Box
      sx={{
        pl: 2.25,
        pr: 0.5,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '40px',
      }}
    >
      <Typography sx={{ lineHeight: 1.25 }}>{name}</Typography>
      {editMode && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton size="small" className="drag-handle" sx={{ cursor: 'move' }}>
            <DragIndicatorIcon fontSize="small" />
          </IconButton>
          <IconButton
            id={`more-button-${id}`}
            aria-controls={open ? `more-menu-${id}` : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
          <Menu
            id={`more-menu-${id}`}
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              'aria-labelledby': `more-button-${id}`,
            }}
          >
            <MenuItem
              onClick={() => {
                onEditChart(index);
                handleClose();
              }}
            >
              <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography>Edit</Typography>
            </MenuItem>
            <MenuItem
              onClick={() => {
                onDeleteChart(index);
                handleClose();
              }}
            >
              <DeleteIcon color="error" fontSize="small" sx={{ mr: 1 }} />
              <Typography color="error">Delete</Typography>
            </MenuItem>
          </Menu>
        </Box>
      )}
    </Box>
  );

  const renderChart = () => {
    switch (chartType) {
      case ChartType.PIE:
        return (
          <PieChart
            data={statsData || {}}
            fieldGroupByColorMapping={fieldGroupByColorMapping}
            fieldGroupByNamingMapping={fieldGroupByNamingMapping}
          />
        );
      case ChartType.BAR:
        return (
          <BarChart
            data={statsData || {}}
            module={moduleType}
            fieldGroupByColorMapping={fieldGroupByColorMapping}
            fieldStackedByColorMapping={fieldStackedByColorMapping}
            fieldStackedByNamingMapping={fieldStackedByNamingMapping}
            fieldGroupByNamingMapping={fieldGroupByNamingMapping}
            orientation={orientation}
            stackedBy={stackedBy}
          />
        );
      case ChartType.LINE:
        return (
          <LineChart
            data={statsData || {}}
            fieldGroupByColorMapping={fieldGroupByColorMapping}
            fieldStackedByColorMapping={fieldStackedByColorMapping}
            fieldStackedByNamingMapping={fieldStackedByNamingMapping}
            fieldGroupByNamingMapping={fieldGroupByNamingMapping}
            startDate={startDate || dayjs().subtract(6, 'months').valueOf()}
            endDate={endDate || dayjs().valueOf()}
            stackedBy={stackedBy}
          />
        );
      case ChartType.PIVOT_TABLE:
        if (!stackedBy) {
          return <Box sx={{ p: 2, textAlign: 'center' }}>Pivot table requires both row and column grouping</Box>;
        }
        return (
          <PivotTable
            data={statsData || {}}
            fieldGroupByNamingMapping={fieldGroupByNamingMapping}
            fieldStackedByNamingMapping={fieldStackedByNamingMapping}
            valueColorRanges={valueColorRanges}
          />
        );
      default:
        return <Box sx={{ p: 2, textAlign: 'center' }}>Unsupported chart type</Box>;
    }
  };

  const renderChartContent = () => {
    if (isLoading) {
      return (
        <Box sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <CircularProgress size={24} />
        </Box>
      );
    }

    if (!statsData || Object.keys(statsData).length === 0) {
      return (
        <Box sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography color="text.secondary">No data available</Typography>
        </Box>
      );
    }

    return renderChart();
  };

  return (
    <Paper sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }} elevation={4}>
      <Box ref={headerRef}>{renderChartHeader()}</Box>
      <Box sx={{ width: '100%', height: headerHeight ? `calc(100% - ${headerHeight}px)` : '100%', flex: 1 }}>
        {renderChartContent()}
      </Box>
    </Paper>
  );
}

export default Chart;
