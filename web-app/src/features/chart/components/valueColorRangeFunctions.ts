import { themeToColor } from '../../../theme';
import { ValueColorRange, paletteColors } from '../chartTypes';

/**
 * Validates that color ranges don't overlap and are properly ordered
 * @param ranges - Array of ValueColorRange objects to validate
 * @returns Error message string if validation fails, null if validation passes
 * @description
 * Performs two validations:
 * 1. Ensures each range's minimum value is less than its maximum value
 * 2. Checks that ranges don't overlap with adjacent ranges
 */
export function validateColorRanges(ranges: ValueColorRange[]): string | null {
  if (ranges.length === 0) return null;

  const sortedRanges = [...ranges].sort((a, b) => a.minValue - b.minValue);

  for (let i = 0; i < sortedRanges.length; i += 1) {
    const range = sortedRanges[i];

    if (range.minValue >= range.maxValue) {
      return `Range ${i + 1}: Minimum value must be less than maximum value`;
    }

    if (i < sortedRanges.length - 1) {
      const nextRange = sortedRanges[i + 1];
      if (range.maxValue > nextRange.minValue) {
        return `Ranges ${i + 1} and ${i + 2} overlap`;
      }
    }
  }

  return null;
}

/**
 * Analyzes numerical data distribution to identify patterns and characteristics
 * @param values - Array of numerical values to analyze
 * @returns Object containing analysis results:
 *   - uniqueValues: Sorted array of unique values
 *   - frequencies: Map of value frequencies
 *   - hasZeros: Boolean indicating presence of zero values
 *   - nonZeroValues: Array of unique non-zero values
 */
function analyzeDataDistribution(values: number[]): {
  uniqueValues: number[];
  frequencies: Map<number, number>;
  hasZeros: boolean;
  nonZeroValues: number[];
} {
  const frequencies = new Map<number, number>();
  values.forEach((value) => {
    frequencies.set(value, (frequencies.get(value) || 0) + 1);
  });

  const uniqueValues = [...frequencies.keys()].sort((a, b) => a - b);
  const hasZeros = uniqueValues.includes(0);
  const nonZeroValues = uniqueValues.filter((v) => v > 0);

  return { uniqueValues, frequencies, hasZeros, nonZeroValues };
}

/**
 * Creates meaningful numerical thresholds based on data distribution analysis
 * @param analysis - Result of analyzeDataDistribution function
 * @returns Array of threshold values that represent natural breakpoints in the data
 * @description
 * Handles several cases:
 * 1. Empty data or only zeros: returns [1]
 * 2. Few unique values (≤4): uses actual values as breakpoints
 * 3. Complex distributions: identifies natural clusters and uses cluster boundaries
 */
function createDataDrivenThresholds(analysis: ReturnType<typeof analyzeDataDistribution>): number[] {
  const { nonZeroValues } = analysis;

  if (nonZeroValues.length === 0) {
    return [1];
  }

  if (nonZeroValues.length <= 4) {
    const thresholds = [...nonZeroValues];
    const maxValue = Math.max(...nonZeroValues);
    if (maxValue <= 10) {
      thresholds.push(maxValue + 1);
    } else {
      thresholds.push(Math.ceil(maxValue * 1.1));
    }
    return thresholds;
  }

  const sortedValues = [...nonZeroValues].sort((a, b) => a - b);
  const clusters: number[][] = [];
  let currentCluster = [sortedValues[0]];

  for (let i = 1; i < sortedValues.length; i += 1) {
    const gap = sortedValues[i] - sortedValues[i - 1];
    const avgValue = Math.ceil((sortedValues[i] + sortedValues[i - 1]) / 2);

    if (gap > Math.max(1, avgValue * 0.5)) {
      clusters.push(currentCluster);
      currentCluster = [sortedValues[i]];
    } else {
      currentCluster.push(sortedValues[i]);
    }
  }
  clusters.push(currentCluster);

  const thresholds: number[] = [];

  for (let i = 0; i < clusters.length; i += 1) {
    const cluster = clusters[i];
    const maxInCluster = Math.max(...cluster);

    if (i === clusters.length - 1) {
      thresholds.push(Math.ceil(maxInCluster + 1));
    } else {
      thresholds.push(Math.ceil(maxInCluster));
    }
  }

  return thresholds;
}

/**
 * Automatically generates color ranges based on the distribution of numerical values
 * @param values - Array of numerical values to analyze
 * @returns Array of ValueColorRange objects defining color mappings
 * @description
 * Handles special cases:
 * 1. Empty data: returns empty array
 * 2. Only zeros: creates single range [0,1]
 * 3. Single non-zero value: creates 1-2 ranges depending on presence of zeros
 * 4. Multiple values: creates ranges based on natural data clusters
 * 5. Missing zero: automatically includes zero as boundary when not present in data
 * Each range is assigned a color from the palette cyclically
 */
export function generateAutoColorRanges(values: number[]): ValueColorRange[] {
  if (values.length === 0) {
    return [];
  }

  // If we have non-zero values but no zero, add zero to ensure complete coverage
  const enhancedValues = values.includes(0) ? values : [0, ...values];

  const analysis = analyzeDataDistribution(enhancedValues);
  const { hasZeros, nonZeroValues } = analysis;

  if (nonZeroValues.length === 0) {
    return [
      {
        minValue: 0,
        maxValue: 1,
        color: themeToColor(paletteColors[0]),
      },
    ];
  }

  // ? Handle edge case: all values are the same non-zero value
  if (nonZeroValues.length === 1) {
    const singleValue = nonZeroValues[0];
    const ranges: ValueColorRange[] = [];

    if (hasZeros) {
      ranges.push({
        minValue: 0,
        maxValue: singleValue,
        color: themeToColor(paletteColors[0]),
      });
    }

    ranges.push({
      minValue: hasZeros ? singleValue : 0,
      maxValue: singleValue + 1,
      color: themeToColor(paletteColors[hasZeros ? 1 : 0]),
    });

    return ranges;
  }

  const thresholds = createDataDrivenThresholds(analysis);

  const ranges: ValueColorRange[] = [];
  // Always start from zero if we have zeros (which we now always do due to enhancement above)
  let currentMin = hasZeros ? 0 : Math.min(...nonZeroValues);

  thresholds.forEach((threshold: number, index: number) => {
    const colorIndex = index % paletteColors.length;
    ranges.push({
      minValue: currentMin,
      maxValue: threshold,
      color: themeToColor(paletteColors[colorIndex]),
    });
    currentMin = threshold;
  });

  return ranges.filter((range) => range.maxValue > range.minValue);
}
