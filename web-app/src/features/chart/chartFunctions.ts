import dayjs from 'dayjs';
import { ChartConfigurationFormInput, ChartSetting, paletteColors, RollingPeriod, ValueColorRange } from './chartTypes';
import { themeToColor } from '../../theme';
import { hasMetaMap, KeyTotal, StatsGroupBy, StatsGroupByFieldMetaMap } from '../stats/statsTypes';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getFieldColor(key: string, fieldColorMapping?: Record<string, string>, groupBy?: StatsGroupBy): string {
  if (fieldColorMapping?.[key]) {
    return fieldColorMapping[key];
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};

/**
 * Sorts an array of ValueColorRange objects by their minimum values in ascending order
 *
 * @param ranges - Array of ValueColorRange objects to sort
 * @returns A new sorted array of ValueColorRange objects
 */
function sortColorRanges(ranges: ValueColorRange[]): ValueColorRange[] {
  return [...ranges].sort((a, b) => a.minValue - b.minValue);
}

/**
 * Determines the appropriate color for a numeric value based on defined color ranges
 *
 * @param value - The numeric value to find a color for
 * @param ranges - Array of ValueColorRange objects defining the color mapping rules
 * @returns The matching color string if found, null otherwise
 * @description
 * For boundary values that fall exactly on range boundaries, the value is assigned
 * to the upper range (the range that starts at the boundary value). This provides
 * intuitive behavior where boundary values belong to the "next" range.
 * Special handling for zero: Zero values are always matched to ranges that include zero,
 * ensuring proper color assignment for missing data points.
 * @example
 * const ranges = [
 *   { minValue: 0, maxValue: 10, color: 'red' },
 *   { minValue: 10, maxValue: 20, color: 'blue' }
 * ];
 * getValueBasedColor(0, ranges); // returns 'red' (includes zero)
 * getValueBasedColor(10, ranges); // returns 'blue' (upper range)
 */
export function getValueRangeColor(value: number, ranges: ValueColorRange[]): string | null {
  if (!ranges || ranges.length === 0) {
    return null;
  }

  const sortedRanges = sortColorRanges(ranges);

  // Special case for zero: find the first range that includes zero
  if (value === 0) {
    for (let i = 0; i < sortedRanges.length; i += 1) {
      const range = sortedRanges[i];
      if (range.minValue <= 0 && range.maxValue >= 0) {
        return range.color;
      }
    }
  }

  // For boundary values, assign to the upper range (more intuitive behavior)
  // Iterate from highest to lowest range to ensure boundary values go to the upper range
  for (let i = sortedRanges.length - 1; i >= 0; i -= 1) {
    const range = sortedRanges[i];
    if (value >= range.minValue && value <= range.maxValue) {
      return range.color;
    }
  }

  return null;
}
