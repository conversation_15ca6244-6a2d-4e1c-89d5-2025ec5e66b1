import { api, buildInvalidatesTags, buildProvidesTags } from '../../api';
import { PaginatedResult } from '../../utils';
import {
  LototoRead,
  LototoParams,
  LototoUpdate,
  LototoDeletable,
  LototoCreate,
  LototoChange,
  LototoPDFParams,
} from './lototoTypes';

export const lototoApi = api
  .enhanceEndpoints({
    addTagTypes: ['Lototo'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getLototos: builder.query<PaginatedResult<LototoRead>, LototoParams>({
        query: (params) => {
          const sort =
            params.sort && params.sort.length > 0 ? params.sort.map((s) => `${s.field}:${s.direction}`) : undefined;
          return {
            url: 'lototo-plans',
            params: {
              ...params,
              sort,
            },
          };
        },
        providesTags: (result) => buildProvidesTags({ rootTag: 'Lototo', response: result?.content }),
      }),
      getLototo: builder.query<LototoRead, number>({
        query: (id) => `lototo-plans/${id}`,
        providesTags: (result) => buildProvidesTags({ rootTag: 'Lototo', response: result?.id }),
      }),
      createLototo: builder.mutation<LototoRead, LototoCreate>({
        query: (body) => ({
          url: 'lototo-plans',
          method: 'POST',
          body,
        }),
        invalidatesTags: () => buildInvalidatesTags({ rootTag: 'Lototo', dependentTypes: ['LototoItem', 'Task'] }),
      }),
      updateLototo: builder.mutation<LototoRead, LototoUpdate>({
        query: (body) => {
          const { id, ...lototoUpdate } = body;
          return {
            url: `lototo-plans/${id}`,
            method: 'PUT',
            body: lototoUpdate,
          };
        },
        invalidatesTags: (result) =>
          buildInvalidatesTags({ rootTag: 'Lototo', id: result?.id, dependentTypes: ['LototoItem', 'Task'] }),
      }),
      getLototoDeletable: builder.query<LototoDeletable, number>({
        query: (id) => `lototo-plans/${id}/deletable`,
      }),
      deleteLototo: builder.mutation<void, number>({
        query: (id) => ({
          url: `lototo-plans/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: (_result, _error, arg) =>
          buildInvalidatesTags({ rootTag: 'Lototo', id: arg, dependentTypes: ['LototoItem', 'Task'] }),
      }),
      cancelLototo: builder.mutation<void, number>({
        query: (id) => ({
          url: `lototo-plans/${id}/cancel`,
          method: 'POST',
        }),
        invalidatesTags: (_result, _error, arg) =>
          buildInvalidatesTags({ rootTag: 'Lototo', id: arg, dependentTypes: ['LototoItem', 'Task'] }),
      }),
      getLototoHistory: builder.query<LototoChange[], number>({
        query: (id) => `lototo-plans/${id}/history`,
        providesTags: (_result, _error, params) => buildProvidesTags({ rootTag: 'Lototo', response: params }),
      }),
      downloadLototoPdf: builder.mutation<void, LototoPDFParams>({
        query: (params) => {
          const { id, ...pdfParams } = params;
          return {
            url: `lototo-plans/${id}/pdf`,
            params: pdfParams,
            method: 'GET',
            cache: 'default',
            responseHandler: async (response) => {
              const hiddenElement = document.createElement('a');
              hiddenElement.target = '_blank';
              hiddenElement.href = window.URL.createObjectURL(await response.blob());
              hiddenElement.click();
            },
          };
        },
      }),
    }),
  });

export const {
  useGetLototosQuery,
  useGetLototoQuery,
  useCreateLototoMutation,
  useUpdateLototoMutation,
  useGetLototoDeletableQuery,
  useDeleteLototoMutation,
  useCancelLototoMutation,
  useGetLototoHistoryQuery,
  useDownloadLototoPdfMutation,
} = lototoApi;
