import { api, buildInvalidatesTags, buildProvidesTags } from '../../../api';
import { PaginatedResult } from '../../../utils';
import {
  LototoItemRead,
  LototoItemParams,
  LototoItemChange,
  LototoItemUpdate,
  LototoItemPDFParams,
} from './lototoItemTypes';

export const lototoItemApi = api
  .enhanceEndpoints({
    addTagTypes: ['LototoItem'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getLototoItems: builder.query<PaginatedResult<LototoItemRead>, LototoItemParams>({
        query: (params) => {
          const sort =
            params.sort && params.sort.length > 0 ? params.sort.map((s) => `${s.field}:${s.direction}`) : undefined;
          return {
            url: 'lototo-plan/items',
            params: {
              ...params,
              sort,
            },
          };
        },
        providesTags: (result) => buildProvidesTags({ rootTag: 'LototoItem', response: result?.content }),
      }),
      getLototoItem: builder.query<LototoItemRead, number>({
        query: (id) => `lototo-plan/items/${id}`,
        providesTags: (result) => buildProvidesTags({ rootTag: 'LototoItem', response: result?.id }),
      }),
      updateLototoItem: builder.mutation<LototoItemRead, LototoItemUpdate>({
        query: (body) => {
          const { id, ...lototoItemUpdate } = body;
          return {
            url: `lototo-plan/items/${id}`,
            method: 'PUT',
            body: lototoItemUpdate,
          };
        },
        invalidatesTags: (result) =>
          buildInvalidatesTags({ rootTag: 'LototoItem', id: result?.id, dependentTypes: ['Lototo'] }),
      }),
      getLototoItemHistory: builder.query<LototoItemChange[], number>({
        query: (id) => `lototo-plan/items/${id}/history`,
        providesTags: (_result, _error, params) => buildProvidesTags({ rootTag: 'LototoItem', response: params }),
      }),
      downloadLototoItemPdf: builder.mutation<void, LototoItemPDFParams>({
        query: (params) => {
          const { id, ...pdfParams } = params;
          return {
            url: `lototo-plan/items/${id}/pdf`,
            params: pdfParams,
            method: 'GET',
            cache: 'default',
            responseHandler: async (response) => {
              const hiddenElement = document.createElement('a');
              hiddenElement.target = '_blank';
              hiddenElement.href = window.URL.createObjectURL(await response.blob());
              hiddenElement.click();
            },
          };
        },
      }),
    }),
  });

export const {
  useGetLototoItemsQuery,
  useGetLototoItemQuery,
  useUpdateLototoItemMutation,
  useGetLototoItemHistoryQuery,
  useDownloadLototoItemPdfMutation,
} = lototoItemApi;
