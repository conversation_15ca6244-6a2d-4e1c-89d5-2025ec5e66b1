import { GroupDisplay } from '../../group/groupTypes';
import { UserDisplay } from '../../user/userTypes';
import type { LototoDisplay } from '../lototoTypes';
import { LototoMethodRead, LototoMethodStatusRead } from '../method/lototoMethodTypes';

export enum LototoItemStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  PENDING_LOTOTO = 'PENDING_LOTOTO',
  ENERGY_FREE = 'ENERGY_FREE',
  PENDING_UNLOTOTO = 'PENDING_UNLOTOTO',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const LototoItemStatusDisplayMap: Record<LototoItemStatus, string> = {
  DRAFT: 'Draft',
  SUBMITTED: 'Submitted',
  APPROVED: 'Approved',
  PENDING_LOTOTO: 'Pending LOTOTO',
  ENERGY_FREE: 'Energy free',
  PENDING_UNLOTOTO: 'Pending UNLOTOTO',
  CLOSED: 'Closed',
  CANCELED: 'Canceled',
};

export enum LototoItemChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const LototoItemChangeTypeDisplayMap: Record<LototoItemChangeType, string> = {
  INSERT: 'LOTOTO item created',
  UPDATE: 'LOTOTO item updated',
  DELETE: 'LOTOTO item deleted',
};

export interface LototoItemFormInput {
  itemRead?: LototoItemNestedRead;
  itemNumber: string;
  description: string;
  method: LototoMethodRead | null;
  initialStatus: LototoMethodStatusRead | null;
  safeStatus: LototoMethodStatusRead | null;
  order: number;
}

export interface LototoItemCreate {
  itemNumber: string;
  description: string;
  method: number;
  initialStatus: number;
  safeStatus?: number;
  lockNumber?: string;
  order: number;
}

export interface LototoItemUpdate {
  id?: number;
  sid?: number;
  itemNumber: string;
  description: string;
  method: number;
  initialStatus: number;
  safeStatus?: number;
  lockNumber?: string;
  order: number;
}

export interface LototoItemPDFParams {
  id: number;
  timeZone: string;
}

export interface LototoItemChange {
  by: UserDisplay;
  at: number;
  type: LototoItemChangeType;
  oldEntity: LototoItemRead;
  newEntity: LototoItemRead;
}

export interface LototoItemViewState {
  listView: 'mine' | 'all';
  group?: GroupDisplay;
  status?: LototoItemStatus;
  candidateGroups?: LototoItemCandidateGroup[];
  createdBy?: UserDisplay;
  search?: string;
  sort?: LototoItemSort[];
  columns?: LototoItemColumnSetting[];
}

export interface LototoItemState {
  lototoItemViewState: LototoItemViewState;
}

export interface LototoItemParams {
  groupId?: number;
  ancestorId?: number;
  status?: LototoItemStatus;
  candidateGroups?: LototoItemCandidateGroup[];
  createdBy?: number;
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
  search?: string;
  sort?: LototoItemSort[];
}

export interface LototoItemRead {
  id: number;
  sid: number;
  group: GroupDisplay;
  processInstanceId: string;
  status: LototoItemStatus;
  itemNumber: string;
  locked: boolean;
  description: string;
  method: LototoMethodRead;
  initialStatus: LototoMethodStatusRead;
  safeStatus?: LototoMethodStatusRead;
  lockNumber: string;
  additionalInfo: string;
  order: number;
  createdBy: UserDisplay;
  createdAt: number;
  lototo: LototoDisplay;
}

export interface LototoItemNestedRead {
  id: number;
  sid: number;
  group: GroupDisplay;
  processInstanceId: string;
  status: LototoItemStatus;
  itemNumber: string;
  locked: boolean;
  description: string;
  method: LototoMethodRead;
  initialStatus: LototoMethodStatusRead;
  safeStatus?: LototoMethodStatusRead;
  lockNumber: string;
  order: number;
  createdBy: UserDisplay;
  createdAt: number;
}

export enum LototoItemCandidateGroup {
  LOTOTO_LOTOTO = 'LOTOTO_LOTOTO',
  LOTOTO_VERIFY_LOTOTO = 'LOTOTO_VERIFY_LOTOTO',
  LOTOTO_UNLOTOTO = 'LOTOTO_UNLOTOTO',
  LOTOTO_VERIFY_UNLOTOTO = 'LOTOTO_VERIFY_UNLOTOTO',
}

export const LototoItemCandidateGroupDisplayMap: Record<LototoItemCandidateGroup, string> = {
  LOTOTO_LOTOTO: 'LOTOTO',
  LOTOTO_VERIFY_LOTOTO: 'Verify LOTOTO',
  LOTOTO_UNLOTOTO: 'UNLOTOTO',
  LOTOTO_VERIFY_UNLOTOTO: 'Verify UNLOTOTO',
};

export enum LototoItemColumn {
  SID = 'sid',
  DESCRIPTION = 'description',
  LOCK_NUMBER = 'lockNumber',
  GROUP = 'group',
  CREATED_BY = 'createdBy',
  LOTOTO = 'lototo',
  STATUS = 'status',
  PLANNED_DATE = 'lototo.plannedDate',
}

export const LototoItemColumnDisplayMap: Record<LototoItemColumn, string> = {
  [LototoItemColumn.SID]: 'ID',
  [LototoItemColumn.DESCRIPTION]: 'Description',
  [LototoItemColumn.LOCK_NUMBER]: 'Lock nr.',
  [LototoItemColumn.GROUP]: 'Group',
  [LototoItemColumn.CREATED_BY]: 'Created By',
  [LototoItemColumn.LOTOTO]: 'LOTOTO plan',
  [LototoItemColumn.STATUS]: 'Status',
  [LototoItemColumn.PLANNED_DATE]: 'Planned date',
};

export interface LototoItemColumnSetting {
  column: LototoItemColumn;
  hidden: boolean;
  width: number;
}

export const LototoItemColumnDefaults: LototoItemColumnSetting[] = [
  { column: LototoItemColumn.SID, width: 75, hidden: false },
  { column: LototoItemColumn.DESCRIPTION, width: 500, hidden: false },
  { column: LototoItemColumn.LOCK_NUMBER, width: 100, hidden: false },
  { column: LototoItemColumn.GROUP, width: 200, hidden: false },
  { column: LototoItemColumn.CREATED_BY, width: 200, hidden: false },
  { column: LototoItemColumn.LOTOTO, width: 200, hidden: false },
  { column: LototoItemColumn.STATUS, width: 120, hidden: false },
  { column: LototoItemColumn.PLANNED_DATE, width: 178, hidden: false },
];

export enum LototoItemSortField {
  ID = 'id',
  SID = 'sid',
  DESCRIPTION = 'description',
  STATUS = 'status',
  LOCK_NUMBER = 'lock_number',
  CREATED_AT = 'created_at',
  UPDATED_AT = 'updated_at',
  PLANNED_DATE = 'planned_date',
}

export const LototoItemFieldSortMap: Record<string, LototoItemSortField> = {
  id: LototoItemSortField.ID,
  sid: LototoItemSortField.SID,
  description: LototoItemSortField.DESCRIPTION,
  status: LototoItemSortField.STATUS,
  lockNumber: LototoItemSortField.LOCK_NUMBER,
  createdAt: LototoItemSortField.CREATED_AT,
  'lototo.plannedDate': LototoItemSortField.PLANNED_DATE,
};

export interface LototoItemSort {
  field: LototoItemSortField;
  direction: 'asc' | 'desc';
}

export enum LototoItemGroupBy {
  GROUP = 'LOTOTO_ITEMS_GROUP',
  PLANNED_DATE_WEEK = 'LOTOTO_ITEMS_PLANNED_DATE_WEEK',
}

export const LototoItemGroupByDisplayMap: Record<LototoItemGroupBy, string> = {
  [LototoItemGroupBy.GROUP]: 'Group',
  [LototoItemGroupBy.PLANNED_DATE_WEEK]: 'Lototo item week',
};
