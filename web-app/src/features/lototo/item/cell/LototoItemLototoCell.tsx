import { Link } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import { Link as RouterLink } from 'react-router-dom';
import { LototoItemRead } from '../lototoItemTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

function LototoItemLototoCell({ value }: GridRenderCellParams<LototoItemRead>) {
  if (!value || !value.id) {
    return null;
  }

  return (
    <Cell>
      <CellText sx={{ display: 'flex', alignItems: 'center' }}>
        <Link
          underline="hover"
          component={RouterLink}
          to={`./../lototo-plans/${value.id}`}
          sx={{ display: 'inline-flex', alignItems: 'center' }}
        >
          #{value.sid}
        </Link>
      </CellText>
      <CellText>{value.title}</CellText>
    </Cell>
  );
}

export default LototoItemLototoCell;
