import { GridRenderCellParams } from '@mui/x-data-grid';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { LototoItemRead, LototoItemStatus, LototoItemStatusDisplayMap } from '../lototoItemTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function LototoItemStatusCell({ value, row }: GridRenderCellParams<LototoItemRead>): JSX.Element {
  const displayValue = LototoItemStatusDisplayMap[value as LototoItemStatus];

  return (
    <Cell title={displayValue}>
      {row.locked ? <LockIcon fontSize="small" /> : <LockOpenIcon fontSize="small" />}
      <CellText>{displayValue}</CellText>
    </Cell>
  );
}
