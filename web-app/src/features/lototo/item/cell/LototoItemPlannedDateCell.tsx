import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { LototoItemRead } from '../lototoItemTypes';
import CellText from '../../../../components/CellText';
import Cell from '../../../../components/Cell';
import { getDatePlusTimeString } from '../../../../utils';

export default function LototoItemPlannedDateCell({ row }: GridRenderCellParams<LototoItemRead>) {
  const { lototo } = row;

  if (!lototo || !lototo.plannedDate) {
    return null;
  }

  const dateString = getDatePlusTimeString(new Date(lototo.plannedDate));

  return (
    <Cell title={dateString}>
      <DateRangeIcon fontSize="small" /> <CellText>{dateString}</CellText>
    </Cell>
  );
}
