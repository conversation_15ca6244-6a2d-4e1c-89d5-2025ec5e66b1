import { GridRenderCellParams } from '@mui/x-data-grid';
import { LototoItemRead } from '../lototoItemTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function LototoItemDescriptionCell({ value }: GridRenderCellParams<LototoItemRead>): JSX.Element {
  return (
    <Cell title={value}>
      <CellText>{value}</CellText>
    </Cell>
  );
}
