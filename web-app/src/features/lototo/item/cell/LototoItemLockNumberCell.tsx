import { GridRenderCellParams } from '@mui/x-data-grid';
import { LototoItemRead } from '../lototoItemTypes';
import CellText from '../../../../components/CellText';
import Cell from '../../../../components/Cell';

export default function LototoItemLockNumberCell({ value }: GridRenderCellParams<LototoItemRead>) {
  if (!value) {
    return null;
  }

  return (
    <Cell title={value}>
      <CellText>{value}</CellText>
    </Cell>
  );
}
