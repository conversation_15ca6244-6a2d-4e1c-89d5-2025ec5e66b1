import { useParams } from 'react-router-dom';
import { useEffect, useState, useMemo } from 'react';
import { TabContext, TabPanel } from '@mui/lab';
import { Box, Tabs, Tab, Paper } from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useGetCurrentUserQuery } from '../../user/userApi';
import { useAppDispatch, useAppSelector } from '../../../store';
import {
  LototoItemColumn,
  LototoItemColumnDefaults,
  LototoItemColumnDisplayMap,
  LototoItemParams,
  LototoItemRead,
  LototoItemStatus,
  LototoItemFieldSortMap,
  LototoItemSort,
} from './lototoItemTypes';
import { useGetLototoItemsQuery } from './lototoItemApi';
import { setLototoItemViewState } from './lototoSlice';
import ErrorGate from '../../../components/ErrorGate';
import PageTitle from '../../title/Title';
import LototoItemFilterBar from './LototoItemFilterBar';
import LototoItemChipFilter from './LototoItemChipFilter';
import { DataGridCellLinkWrapper } from '../../../components/DataGridCellLink';
import GroupCell from '../../../components/GroupCell';
import SidCell from '../../../components/SidCell';
import LototoItemDescriptionCell from './cell/LototoItemDescriptionCell';
import LototoItemLockNumberCell from './cell/LototoItemLockNumberCell';
import LototoItemLototoCell from './cell/LototoItemLototoCell';
import LototoItemPlannedDateCell from './cell/LototoItemPlannedDateCell';
import LototoItemStatusCell from './cell/LototoItemStatusCell';
import NoRowsOverlay from '../../../components/NoRowsOverlay';
import { GroupDisplay } from '../../group/groupTypes';
import { UserDisplay } from '../../user/userTypes';
import { getDatePlusTimeString } from '../../../utils';
import UserCell from '../../../components/UserCell';

const getLototoItemUrl = (lototoItemId: number) => `./${lototoItemId}`;

// Convert the GridSortModel to the backend sort format
const getSortFromGridModel = (model: GridSortModel): LototoItemSort[] =>
  model
    .map((item) => {
      const mappedField = LototoItemFieldSortMap[item.field as keyof LototoItemRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort || 'asc',
      };
    })
    .filter((sort): sort is LototoItemSort => sort !== null);

// Convert the backend sort format to the GridSortModel
const getGridModelFromSort = (sort: LototoItemSort[]): GridSortModel =>
  sort
    .map((item) => {
      // Find the grid field whose value in LototoItemFieldSortMap equals the backend sort field
      const gridField = Object.entries(LototoItemFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

export const columnDefaults: Record<LototoItemColumn, GridColDef<LototoItemRead>> = {
  [LototoItemColumn.SID]: {
    field: LototoItemColumn.SID,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(SidCell(params), getLototoItemUrl(params.row.id)),
  },
  [LototoItemColumn.DESCRIPTION]: {
    field: LototoItemColumn.DESCRIPTION,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(LototoItemDescriptionCell(params), getLototoItemUrl(params.row.id)),
  },
  [LototoItemColumn.LOCK_NUMBER]: {
    field: LototoItemColumn.LOCK_NUMBER,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.LOCK_NUMBER],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(LototoItemLockNumberCell(params), getLototoItemUrl(params.row.id)),
  },
  [LototoItemColumn.GROUP]: {
    field: LototoItemColumn.GROUP,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(GroupCell(params), getLototoItemUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [LototoItemColumn.CREATED_BY]: {
    field: LototoItemColumn.CREATED_BY,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.CREATED_BY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(UserCell(params), getLototoItemUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [LototoItemColumn.LOTOTO]: {
    field: LototoItemColumn.LOTOTO,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.LOTOTO],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(LototoItemLototoCell(params), getLototoItemUrl(params.row.id)),
  },
  [LototoItemColumn.STATUS]: {
    field: LototoItemColumn.STATUS,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.STATUS],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(LototoItemStatusCell(params), getLototoItemUrl(params.row.id)),
  },
  [LototoItemColumn.PLANNED_DATE]: {
    field: LototoItemColumn.PLANNED_DATE,
    headerName: LototoItemColumnDisplayMap[LototoItemColumn.PLANNED_DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoItemRead>) =>
      DataGridCellLinkWrapper(LototoItemPlannedDateCell(params), getLototoItemUrl(params.row.id)),
    valueGetter: (value: number | undefined) => (value == null ? undefined : new Date(value)),
    valueFormatter: (value) => {
      if (!value) {
        return '';
      }
      return getDatePlusTimeString(value);
    },
  },
};

function LototoItemListPage() {
  const { data: me } = useGetCurrentUserQuery();
  const { groupId } = useParams();
  const lototoItemViewState = useAppSelector((state) => state.lototoItem.lototoItemViewState);

  const dispatch = useAppDispatch();
  const defaultPageSize = 25;
  const [sortModel, setSortModel] = useState<GridSortModel>(
    lototoItemViewState?.sort ? getGridModelFromSort(lototoItemViewState.sort) : []
  );

  const getCandidateGroups = () =>
    lototoItemViewState?.candidateGroups?.length && lototoItemViewState.candidateGroups.length > 0
      ? lototoItemViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || lototoItemViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${LototoItemStatus.CLOSED}&statusNot=${LototoItemStatus.CANCELED}`;
  };

  const [params, setParams] = useState<LototoItemParams>({
    ancestorId: Number(groupId),
    groupId: lototoItemViewState.group?.id,
    search: lototoItemViewState.search,
    status: lototoItemViewState.status,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    pageSize: defaultPageSize,
    pageNumber: 0,
    sort: lototoItemViewState?.sort,
  });

  const { data, isLoading, error } = useGetLototoItemsQuery(params);
  const [page, setPage] = useState<number>(0);

  useEffect(() => {
    if (lototoItemViewState) {
      setParams((prev) => ({
        ...prev,
        groupId: lototoItemViewState?.group?.id,
        search: lototoItemViewState?.search,
        status: lototoItemViewState?.status,
        candidateGroups: getCandidateGroups(),
        sort: lototoItemViewState?.sort,
      }));

      if (lototoItemViewState.sort) {
        setSortModel(getGridModelFromSort(lototoItemViewState.sort));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lototoItemViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    dispatch(
      setLototoItemViewState({
        ...lototoItemViewState,
        listView: view,
        candidateGroups: getCandidateGroups(),
      })
    );
    setParams((prev) => ({
      ...prev,
      pageNumber: 0,
      filter: getFilter(view),
    }));
    setPage(0);
  };

  const handlePaginationChange = (model: { page: number; pageSize: number }) => {
    setPage(model.page);
    setParams((prev) => ({
      ...prev,
      pageNumber: model.page,
    }));
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize: defaultPageSize });
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    const newSort = getSortFromGridModel(newModel);
    dispatch(setLototoItemViewState({ ...lototoItemViewState, sort: newSort }));
    setParams((prev) => ({
      ...prev,
      sort: newSort,
    }));
  };

  const columns = useMemo(() => {
    const cols = lototoItemViewState.columns ? lototoItemViewState.columns : LototoItemColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [lototoItemViewState.columns]);

  return (
    <ErrorGate error={error}>
      <PageTitle page="LOTOTO plans" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={lototoItemViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
        </Box>
        <LototoItemChipFilter me={me} resetPageNumber={resetPageNumber} />
        <LototoItemFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={!isLoading && data ? data.content : []}
                columns={columns}
                rowCount={!isLoading && data ? data.total : 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize: defaultPageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                getRowId={(row) => row.id}
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No LOTOTO items found' },
                }}
                onColumnWidthChange={(colParams) => {
                  const newViewState = { ...lototoItemViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === colParams.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: colParams.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setLototoItemViewState(newViewState));
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default LototoItemListPage;
