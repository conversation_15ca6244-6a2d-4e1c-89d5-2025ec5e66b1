import { Link, useParams } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { TabContext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useGetCurrentUserQuery } from '../user/userApi';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  LototoColumn,
  LototoColumnDefaults,
  LototoColumnDisplayMap,
  LototoFieldSortMap,
  LototoParams,
  LototoRead,
  LototoSort,
  LototoStatus,
} from './lototoTypes';
import { PermissionType } from '../guard/guardTypes';
import { UserRole } from '../user/userTypes';
import { GuardResult } from '../guard/guardHooks';
import { useGetLototosQuery } from './lototoApi';
import { setLototoViewState } from './lototoSlice';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import Guard from '../guard/Guard';
import ResponsiveButton from '../../components/ResponsiveButton';
import LototoChipFilter from './LototoChipFilter';
import LototoFilterBar from './LototoFilterBar';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import SidCell from '../../components/SidCell';
import GroupCell from '../../components/GroupCell';
import { GroupDisplay } from '../group/groupTypes';
import LototoTitleCell from './cell/LototoTitleCell';
import LototoLockNumberCell from './cell/LototoLockNumberCell';
import LototoPlannedDateCell from './cell/LototoPlannedDateCell';
import LototoStatusCell from './cell/LototoStatusCell';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';
import { getDatePlusTimeString } from '../../utils';

const getLototoUrl = (lototoId: number) => `./${lototoId}`;

const getSortFromGridModel = (model: GridSortModel): LototoSort[] =>
  model
    .map((item) => {
      const mappedField = LototoFieldSortMap[item.field as keyof LototoRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is LototoSort => sort !== null);

const getGridModelFromSort = (sort: LototoSort[]): GridSortModel =>
  sort
    .map((item) => {
      // Find the grid field whose value in LototoFieldSortMap equals the backend sort field
      const gridField = Object.entries(LototoFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

const columnDefaults: Record<LototoColumn, GridColDef<LototoRead>> = {
  [LototoColumn.SID]: {
    field: LototoColumn.SID,
    headerName: LototoColumnDisplayMap[LototoColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(SidCell(params), getLototoUrl(params.row.id)),
  },
  [LototoColumn.TITLE]: {
    field: LototoColumn.TITLE,
    headerName: LototoColumnDisplayMap[LototoColumn.TITLE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(LototoTitleCell(params), getLototoUrl(params.row.id)),
  },
  [LototoColumn.GROUP]: {
    field: LototoColumn.GROUP,
    headerName: LototoColumnDisplayMap[LototoColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(GroupCell(params), getLototoUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [LototoColumn.STATUS]: {
    field: LototoColumn.STATUS,
    headerName: LototoColumnDisplayMap[LototoColumn.STATUS],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(LototoStatusCell(params), getLototoUrl(params.row.id)),
  },
  [LototoColumn.LOCK_NUMBER]: {
    field: LototoColumn.LOCK_NUMBER,
    headerName: LototoColumnDisplayMap[LototoColumn.LOCK_NUMBER],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(LototoLockNumberCell(params), getLototoUrl(params.row.id)),
  },
  [LototoColumn.PLANNED_DATE]: {
    field: LototoColumn.PLANNED_DATE,
    headerName: LototoColumnDisplayMap[LototoColumn.PLANNED_DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<LototoRead>) =>
      DataGridCellLinkWrapper(LototoPlannedDateCell(params), getLototoUrl(params.row.id)),
    valueGetter: (value: number | undefined) => (value == null ? undefined : new Date(value)),
    valueFormatter: (value: Date | undefined) => {
      if (!value) {
        return '';
      }
      return getDatePlusTimeString(value);
    },
  },
};

function LototoListPage() {
  const { data: me } = useGetCurrentUserQuery();
  const { groupId } = useParams();
  const lototoViewState = useAppSelector((state) => state.lototo.lototoViewState);
  const dispatch = useAppDispatch();
  const { page, setPage, pageSize, setPageSize } = usePaging();
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(lototoViewState?.sort || []));

  const columns = useMemo(() => {
    const cols = lototoViewState.columns ? lototoViewState.columns : LototoColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [lototoViewState.columns]);

  const getCandidateGroups = () =>
    lototoViewState?.candidateGroups?.length && lototoViewState.candidateGroups.length > 0
      ? lototoViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || lototoViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${LototoStatus.CLOSED}&statusNot=${LototoStatus.CANCELED}`;
  };

  const [queryParams, setQueryParams] = useState<LototoParams>({
    ancestorGroupId: Number(groupId),
    createdBy: lototoViewState.createdBy?.id,
    groupId: lototoViewState.group?.id,
    search: lototoViewState.search,
    status: lototoViewState.status,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    pageSize,
    pageNumber: page,
    sort: lototoViewState?.sort,
  });

  const { data, isLoading, error } = useGetLototosQuery(queryParams);
  const canRequestLototo = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.LOTOTO_CREATE);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageNumber: page,
      pageSize,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (lototoViewState) {
      setQueryParams((prev) => ({
        ...prev,
        groupId: lototoViewState?.group?.id,
        createdBy: lototoViewState?.createdBy?.id,
        search: lototoViewState?.search,
        status: lototoViewState?.status,
        candidateGroups: getCandidateGroups(),
        sort: lototoViewState?.sort,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lototoViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setLototoViewState({
        ...lototoViewState,
        listView: view,
        candidateGroups: getCandidateGroups(),
      })
    );
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(view),
    }));
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setLototoViewState({ ...lototoViewState, sort: getSortFromGridModel(newModel) }));
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="LOTOTO plans" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={lototoViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Guard hasAccess={canRequestLototo}>
            <Box>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Create plan
              </ResponsiveButton>
            </Box>
          </Guard>
        </Box>
        <LototoChipFilter me={me} resetPageNumber={resetPageNumber} />
        <LototoFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          value="0"
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...lototoViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setLototoViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No LOTOTO plans found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}
export default LototoListPage;
