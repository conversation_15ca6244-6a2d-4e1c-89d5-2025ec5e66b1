import { Dayjs } from 'dayjs';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { LototoItemFormInput, LototoItemNestedRead, LototoItemUpdate } from './item/lototoItemTypes';

export interface LototoParams {
  groupId?: number;
  createdBy?: number;
  ancestorGroupId?: number;
  status?: LototoStatus;
  candidateGroups?: LototoCandidatGroup[];
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
  search?: string;
  sort?: LototoSort[];
}

export interface LototoFormInput {
  group: GroupDisplay | null;
  title: string;
  status: LototoStatus;
  description: string;
  lockNumber: string;
  items: LototoItemFormInput[];
  documents: FileDisplay[];
  plannedDate: Dayjs;
}

export interface LototoCopy {
  title: string;
  description: string;
  items: LototoItemFormInput[];
  documents: FileDisplay[];
}

export interface LototoPDFParams {
  id: number;
  timeZone: string;
}

export enum LototoCandidatGroup {
  LOTOTO_CREATE = 'LOTOTO_CREATE',
  LOTOTO_APPROVE = 'LOTOTO_APPROVE',
  LOTOTO_LOCK = 'LOTOTO_LOCK',
  LOTOTO_UNLOCK = 'LOTOTO_UNLOCK',
}

export const LototoCandidateGroupDisplayMap: Record<LototoCandidatGroup, string> = {
  LOTOTO_CREATE: 'Submit',
  LOTOTO_APPROVE: 'Approve',
  LOTOTO_LOCK: 'Lock',
  LOTOTO_UNLOCK: 'Unlock',
};

export interface LototoViewState {
  listView: 'mine' | 'all';
  group?: GroupDisplay;
  status?: LototoStatus;
  candidateGroups?: LototoCandidatGroup[];
  createdBy?: User;
  search?: string;
  sort?: LototoSort[];
  columns?: LototoColumnSetting[];
}

export interface LototoState {
  lototoCopy?: Partial<LototoCopy>;
  lototoViewState: LototoViewState;
}

export enum LototoStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  ENERGY_FREE = 'ENERGY_FREE',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const LototoStatusDisplayMap: Record<LototoStatus, string> = {
  DRAFT: 'Draft',
  SUBMITTED: 'Submitted',
  APPROVED: 'Approved',
  ENERGY_FREE: 'Energy free',
  CLOSED: 'Closed',
  CANCELED: 'Canceled',
};

export interface LototoRead {
  id: number;
  sid: number;
  processInstanceId: string;
  locked: boolean;
  group: GroupDisplay;
  title: string;
  description: string;
  status: LototoStatus;
  items: LototoItemNestedRead[];
  documents: FileDisplay[];
  plannedDate: number;
  lockNumber: string;
  createdBy: User;
  createdAt: number;
  modifiedBy: User;
  modifiedAt: number;
}

export interface LototoDisplay {
  id: number;
  sid: number;
  locked: boolean;
  title: string;
  status: LototoStatus;
  lockNumber: string;
  plannedDate: number;
}

export interface LototoCreate {
  title: string;
  description: string;
  group: number;
  items: LototoItemUpdate[];
  documents: number[];
  plannedDate: number;
  lockNumber: string;
  draft?: boolean;
}

export interface LototoUpdate {
  id: number;
  title: string;
  description: string;
  items: LototoItemUpdate[];
  documents: number[];
  plannedDate: number;
  lockNumber: string;
}

export interface LototoDeletable {
  deletable: boolean;
}

export enum LototoChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const LototoChangeTypeDisplayMap: Record<LototoChangeType, string> = {
  INSERT: 'LOTOTO plan created',
  UPDATE: 'LOTOTO plan updated',
  DELETE: 'LOTOTO plan deleted',
};

export interface LototoChange {
  by: UserDisplay;
  at: number;
  type: LototoChangeType;
  oldEntity: LototoRead;
  newEntity: LototoRead;
}

export enum LototoColumn {
  SID = 'sid',
  TITLE = 'title',
  GROUP = 'group',
  STATUS = 'status',
  LOCK_NUMBER = 'lockNumber',
  PLANNED_DATE = 'plannedDate',
}

export const LototoColumnDisplayMap: Record<LototoColumn, string> = {
  [LototoColumn.SID]: 'ID',
  [LototoColumn.TITLE]: 'Title',
  [LototoColumn.GROUP]: 'Created by',
  [LototoColumn.STATUS]: 'Status',
  [LototoColumn.LOCK_NUMBER]: 'Box number',
  [LototoColumn.PLANNED_DATE]: 'Planned date',
};

export interface LototoColumnSetting {
  column: LototoColumn;
  hidden: boolean;
  width: number;
}

export const LototoColumnDefaults: LototoColumnSetting[] = [
  { column: LototoColumn.SID, width: 75, hidden: false },
  { column: LototoColumn.TITLE, width: 865, hidden: false },
  { column: LototoColumn.GROUP, width: 200, hidden: false },
  { column: LototoColumn.STATUS, width: 150, hidden: false },
  { column: LototoColumn.LOCK_NUMBER, width: 150, hidden: false },
  { column: LototoColumn.PLANNED_DATE, width: 150, hidden: false },
];

export enum LototoSortField {
  SID = 'sid',
  TITLE = 'title',
  STATUS = 'status',
  PLANNED_DATE = 'planned_date',
  CREATION_DATE = 'creation_date',
  MODIFIED_DATE = 'modified_date',
}

export const LototoFieldSortMap: Partial<Record<keyof LototoRead, LototoSortField>> = {
  sid: LototoSortField.SID,
  title: LototoSortField.TITLE,
  status: LototoSortField.STATUS,
  plannedDate: LototoSortField.PLANNED_DATE,
};

export interface LototoSort {
  field: LototoSortField;
  direction: 'asc' | 'desc';
}

export enum LototoGroupBy {
  GROUP = 'LOTOTO_PLANS_GROUP',
  PLANNED_DATE_WEEK = 'LOTOTO_PLANS_PLANNED_DATE_WEEK',
}

export const LototoGroupByDisplayMap: Record<LototoGroupBy, string> = {
  [LototoGroupBy.GROUP]: 'Group',
  [LototoGroupBy.PLANNED_DATE_WEEK]: 'Lototo plan week',
};
