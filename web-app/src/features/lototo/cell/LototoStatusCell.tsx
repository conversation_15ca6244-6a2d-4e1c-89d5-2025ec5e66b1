import { GridRenderCellParams } from '@mui/x-data-grid';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import { LototoRead, LototoStatus, LototoStatusDisplayMap } from '../lototoTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type LototoStatusCellParam = GridRenderCellParams<LototoRead, LototoStatus, string>;

export default function LototoStatusCell(params: LototoStatusCellParam) {
  const { row, value: status, formattedValue: statusText } = params;

  if (!status || !statusText) {
    return null;
  }

  const StatusIcon = row.locked ? LockIcon : LockOpenIcon;

  return (
    <Cell title={statusText}>
      <StatusIcon fontSize="small" /> <CellText>{LototoStatusDisplayMap[status]}</CellText>
    </Cell>
  );
}
