import { GridRenderCellParams } from '@mui/x-data-grid';
import { LototoRead } from '../lototoTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type LototoTitleCellParam = GridRenderCellParams<LototoRead, string>;

export default function LototoTitleCell(params: LototoTitleCellParam) {
  const { formattedValue: title } = params;

  if (!title || title.length === 0) {
    return null;
  }

  return (
    <Cell title={title}>
      <CellText>{title}</CellText>
    </Cell>
  );
}
