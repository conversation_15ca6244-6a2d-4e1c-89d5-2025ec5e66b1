import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { LototoRead } from '../lototoTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type LototoPlannedDateCellParam = GridRenderCellParams<LototoRead, number>;

export default function LototoPlannedDateCell(params: LototoPlannedDateCellParam) {
  const { formattedValue: value } = params;

  if (!value) {
    return null;
  }

  const dateString = new Date(value).toDateString();

  return (
    <Cell title={dateString}>
      <DateRangeIcon fontSize="small" /> <CellText>{dateString}</CellText>
    </Cell>
  );
}
