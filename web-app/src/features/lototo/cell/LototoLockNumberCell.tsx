import { GridRenderCellParams } from '@mui/x-data-grid';
import { LototoRead } from '../lototoTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type LototoLockNumberCellParam = GridRenderCellParams<LototoRead, string>;

export default function LototoLockNumberCell(params: LototoLockNumberCellParam) {
  const { formattedValue: lockNumber } = params;

  if (!lockNumber || lockNumber.length === 0) {
    return null;
  }

  return (
    <Cell title={lockNumber}>
      <CellText>{lockNumber}</CellText>
    </Cell>
  );
}
