import { Dayjs } from 'dayjs';
import { GroupDisplay } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { FileDisplay } from '../file/fileTypes';
import { EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';

export interface InstructionParams {
  groupId?: number;
  ancestorGroupId?: number;
  pathGroupId?: number;
  startDateLte?: number;
  startDateGte?: number;
  endDateLte?: number;
  endDateGte?: number;
  endDateNotNull?: boolean;
  endDateNull?: boolean;
  status?: InstructionStatus;
  statusNot?: InstructionStatus;
  candidateGroups?: InstructionCandidateGroup[];
  createdBy?: number;
  filter?: string;
  search?: string;
  pageNumber?: number;
  pageSize?: number;
}

export interface InstructionPdfParams {
  id: number;
  timeZone: string;
}

export interface InstructionFormInput {
  group: GroupDisplay | null;
  description: string;
  startTime: Dayjs;
  endTime: Dayjs | null;
  files: FileDisplay[];
}

export interface InstructionViewState {
  listView: 'mine' | 'all';
  group?: GroupDisplay;
  status?: InstructionStatus;
  search?: string;
  candidateGroups?: InstructionCandidateGroup[];
  createdBy?: User;
}

export interface InstructionState {
  instructionCopy?: Partial<InstructionCopy>;
  instructionViewState: InstructionViewState;
}

export enum InstructionCandidateGroup {
  INSTRUCTION_CLOSE = 'INSTRUCTION_CLOSE',
}

export const InstructionCandidateGroupDisplayMap: Record<InstructionCandidateGroup, string> = {
  INSTRUCTION_CLOSE: 'Close',
};

export interface InstructionCreate {
  description: string;
  group: number;
  startTime: number;
  endTime?: number;
  files: number[];
}
export enum InstructionStatus {
  PLANNED = 'PLANNED',
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const InstructionStatusMeta: EnumMetaMap<InstructionStatus> = {
  [InstructionStatus.PLANNED]: { label: 'Planned', color: themeToColor('primary.main') },
  [InstructionStatus.ACTIVE]: { label: 'Active', color: themeToColor('secondary.main') },
  [InstructionStatus.CLOSED]: { label: 'Closed', color: themeToColor('success.main') },
  [InstructionStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const InstructionStatusDisplayMap = Object.fromEntries(
  Object.entries(InstructionStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<InstructionStatus, string>;

export interface InstructionRead {
  id: number;
  sid: number;
  description: string;
  group: GroupDisplay;
  status: InstructionStatus;
  processInstanceId: string;
  startTime: number;
  endTime: number;
  locked: boolean;
  createdBy: UserDisplay;
  files: FileDisplay[];
}

export interface InstructionCopy {
  description: string;
}

export interface InstructionUpdate {
  id: number;
  description?: string;
  startTime?: number;
  endTime?: number;
  files: number[];
}

export interface InstructionDeletable {
  deletable: boolean;
}

export enum InstructionChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const InstructionChangeTypeDisplayMap: Record<InstructionChangeType, string> = {
  INSERT: 'Instruction created',
  UPDATE: 'Instruction updated',
  DELETE: 'Instruction deleted',
};

export interface InstructionChange {
  by: UserDisplay;
  at: number;
  type: InstructionChangeType;
  oldEntity: InstructionRead;
  newEntity: InstructionRead;
}

export enum InstructionGroupBy {
  GROUP = 'INSTRUCTIONS_GROUP',
  CREATED_BY = 'INSTRUCTIONS_CREATED_BY',
  STATUS = 'INSTRUCTIONS_STATUS',
  CREATION_DATE_WEEK = 'INSTRUCTIONS_CREATION_DATE_WEEK',
}

export const InstructionGroupByDisplayMap: Record<InstructionGroupBy, string> = {
  [InstructionGroupBy.GROUP]: 'Group',
  [InstructionGroupBy.CREATED_BY]: 'User',
  [InstructionGroupBy.STATUS]: 'Status',
  [InstructionGroupBy.CREATION_DATE_WEEK]: 'Instruction week',
};

export interface InstructionGroupByFieldType {
  [InstructionGroupBy.STATUS]: InstructionStatus;
}

export const InstructionGroupByFieldMetaMap: {
  [K in keyof InstructionGroupByFieldType]: EnumMetaMap<InstructionGroupByFieldType[K]>;
} = {
  [InstructionGroupBy.STATUS]: InstructionStatusMeta,
};
