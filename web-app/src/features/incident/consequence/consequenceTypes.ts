import { themeToColor } from '../../../theme';
import { EnumMetaMap } from '../../../types';

export interface ConsequenceParams {
  enabled?: boolean;
  pageSize?: number;
}

export interface ConsequenceForm {
  name: string;
  enabled: boolean;
  order: number;
  category: ConsequenceCategoryRead | null;
  severity: Severity | null;
}

export interface ConsequenceCreate {
  name: string;
  enabled: boolean;
  order: number;
  category: number;
  severity: Severity;
}

export interface ConsequenceUpdate {
  id: number;
  name: string;
  enabled: boolean;
  order: number;
  category: number;
  severity: Severity;
}

export interface ConsequenceRead {
  id: number;
  name: string;
  enabled: boolean;
  order: number;
  category: ConsequenceCategoryRead;
  severity: Severity;
}

export interface ConsequenceDeletable {
  deletable: boolean;
}

export interface ConsequenceCategoryCreate {
  name: string;
  order: number;
}

export interface ConsequenceCategoryUpdate {
  id: number;
  name: string;
  order: number;
}

export interface ConsequenceCategoryRead {
  id: number;
  name: string;
  order: number;
}

export interface ConsequenceCategoryDeletable {
  deletable: boolean;
}

export interface IncidentConsequenceCreate {
  consequence: number;
  required: boolean;
}

export interface IncidentConsequenceUpdate {
  id: number;
  consequence: number;
  required: boolean;
}

export interface IncidentConsequenceRead {
  id: number;
  consequence: ConsequenceRead;
  required: boolean;
}

export interface IncidentConsequenceForm {
  id?: number;
  consequence: ConsequenceRead;
  name: string;
  required: boolean;
}

export enum Severity {
  UNSAFE_ACT = 'UNSAFE_ACT',
  NEAR_MISS = 'NEAR_MISS',
  MINOR = 'MINOR',
  SERIOUS = 'SERIOUS',
  CATASTROFIC = 'CATASTROFIC',
}

export const SeverityMeta: EnumMetaMap<Severity> = {
  [Severity.UNSAFE_ACT]: { label: 'Negligible', color: themeToColor('success.main') },
  [Severity.NEAR_MISS]: { label: 'Minor', color: 'rgb(255, 235, 59)' },
  [Severity.MINOR]: { label: 'Moderate', color: themeToColor('warning.light') },
  [Severity.SERIOUS]: { label: 'Major', color: themeToColor('error.light') },
  [Severity.CATASTROFIC]: { label: 'Catastrophic', color: themeToColor('error.dark') },
};

export const SeverityDisplayMap = Object.fromEntries(
  Object.entries(SeverityMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<Severity, string>;

export const SeverityColorMap = Object.fromEntries(
  Object.entries(SeverityMeta).map(([key, meta]) => [key, meta?.color ?? ''])
) as Record<Severity, string>;

export enum Risk {
  VERY_LOW = 'VERY_LOW',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY_HIGH',
}

export const RiskMeta: EnumMetaMap<Risk> = {
  [Risk.VERY_LOW]: { label: 'Very low', color: themeToColor('success.main') },
  [Risk.LOW]: { label: 'Low', color: 'rgb(255, 235, 59)' },
  [Risk.MEDIUM]: { label: 'Medium', color: themeToColor('warning.light') },
  [Risk.HIGH]: { label: 'High', color: themeToColor('error.light') },
  [Risk.VERY_HIGH]: { label: 'Very high', color: themeToColor('error.dark') },
};

export const RiskDisplayMap = Object.fromEntries(
  Object.entries(RiskMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<Risk, string>;

export const RiskColorMap = Object.fromEntries(
  Object.entries(RiskMeta).map(([key, meta]) => [key, meta?.color ?? ''])
) as Record<Risk, string>;

export enum Frequency {
  VERY_UNLIKELY = 'VERY_UNLIKELY',
  UNLIKELY = 'UNLIKELY',
  POSSIBLE = 'POSSIBLE',
  LIKELY = 'LIKELY',
  VERY_LIKELY = 'VERY_LIKELY',
}

export const FrequencyMeta: EnumMetaMap<Frequency> = {
  [Frequency.VERY_UNLIKELY]: { label: 'Very unlikely', color: themeToColor('success.main') },
  [Frequency.UNLIKELY]: { label: 'Unlikely', color: 'rgb(255, 235, 59)' },
  [Frequency.POSSIBLE]: { label: 'Possible', color: themeToColor('warning.light') },
  [Frequency.LIKELY]: { label: 'Likely', color: themeToColor('error.light') },
  [Frequency.VERY_LIKELY]: { label: 'Very likely', color: themeToColor('error.dark') },
};

export const FrequencyDisplayMap = Object.fromEntries(
  Object.entries(FrequencyMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<Frequency, string>;

export const FrequencyColorMap = Object.fromEntries(
  Object.entries(FrequencyMeta).map(([key, meta]) => [key, meta?.color ?? ''])
) as Record<Frequency, string>;

export const FrequencyHelperMap: Record<Frequency, string> = {
  VERY_UNLIKELY: 'Never heard of in industry',
  UNLIKELY: 'Heard of incident in industry',
  POSSIBLE: 'Has occurred in company',
  LIKELY: 'Several times per year in company',
  VERY_LIKELY: 'Several times per year at location',
};
