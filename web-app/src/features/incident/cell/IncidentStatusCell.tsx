import { GridRenderCellParams } from '@mui/x-data-grid';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { Box } from '@mui/material';
import { IncidentListRead, IncidentStatusDisplayMap } from '../incidentTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type IncidentStatusCellParam = GridRenderCellParams<IncidentListRead>;

export default function IncidentStatusCell(params: IncidentStatusCellParam) {
  const {
    row: { status, locked },
  } = params;

  if (!status) {
    return null;
  }

  return (
    <Cell title={IncidentStatusDisplayMap[status]}>
      <Box display="flex" alignItems="center" gap={0.5}>
        {locked ? (
          <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
        ) : (
          <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
        )}
        <CellText>{IncidentStatusDisplayMap[status]}</CellText>
      </Box>
    </Cell>
  );
}
