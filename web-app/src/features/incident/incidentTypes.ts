import { Dayjs } from 'dayjs';
import {
  Frequency,
  IncidentConsequenceCreate,
  IncidentConsequenceRead,
  IncidentConsequenceUpdate,
  IncidentConsequenceForm,
  Risk,
  Severity,
  SeverityMeta,
  RiskMeta,
  FrequencyMeta,
} from './consequence/consequenceTypes';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { UserDisplay } from '../user/userTypes';
import { IncidentCauseCreate, IncidentCauseRead, IncidentCauseUpdate, IncidentCauseForm } from './cause/causeTypes';
import { LocationDisplay, LocationFilterMode } from '../location/locationTypes';
import { EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';

export interface IncidentParams {
  search?: string;
  ancestorGroupId?: number;
  groupId?: number;
  status?: IncidentStatus;
  statusNot?: IncidentStatus;
  filter?: string;
  severity?: Severity;
  risk?: Risk;
  candidateGroups?: IncidentCandidatGroup[];
  createdBy?: number;
  pageNumber?: number;
  pageSize?: number;
  sort?: IncidentSort[];
  locationId?: number;
  ancestorLocationId?: number;
}

export interface IncidentPdfParams {
  id: number;
  timeZone: string;
  withFiles: boolean;
}

export interface IncidentViewState {
  listView: 'mine' | 'all';
  viewMode?: 'table' | 'map';
  search?: string;
  severity?: Severity;
  risk?: Risk;
  group?: GroupListRead;
  status?: IncidentStatus;
  columns?: IncidentColumnSetting[];
  candidateGroups?: IncidentCandidatGroup[];
  createdBy?: number;
  sort?: IncidentSort[];
  location?: LocationDisplay;
  locationFilterMode?: LocationFilterMode;
}

export enum IncidentCandidatGroup {
  INCIDENT_INVESTIGATE = 'INCIDENT_INVESTIGATE',
  INCIDENT_RESOLVE = 'INCIDENT_RESOLVE',
  INCIDENT_APPROVE_MEDIUM_RISK = 'INCIDENT_APPROVE_MEDIUM_RISK',
  INCIDENT_APPROVE_HIGH_RISK = 'INCIDENT_APPROVE_HIGH_RISK',
}

export const IncidentCandidateGroupDisplayMap: Record<IncidentCandidatGroup, string> = {
  INCIDENT_INVESTIGATE: 'Investigate',
  INCIDENT_RESOLVE: 'Resolve',
  INCIDENT_APPROVE_MEDIUM_RISK: 'Approve medium risk',
  INCIDENT_APPROVE_HIGH_RISK: 'Approve high risk',
};

export enum IncidentStatus {
  REPORTED = 'REPORTED',
  INVESTIGATED = 'INVESTIGATED',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const IncidentStatusMeta: EnumMetaMap<IncidentStatus> = {
  [IncidentStatus.REPORTED]: { label: 'Reported', color: themeToColor('primary.main') },
  [IncidentStatus.INVESTIGATED]: { label: 'Investigated', color: themeToColor('secondary.main') },
  [IncidentStatus.RESOLVED]: { label: 'Resolved', color: themeToColor('success.main') },
  [IncidentStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const IncidentStatusDisplayMap = Object.fromEntries(
  Object.entries(IncidentStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<IncidentStatus, string>;

export interface IncidentFormInput {
  group: GroupDisplay | null;
  name: string;
  description: string;
  location: LocationDisplay | null;
  involvedPersons: string;
  date: Dayjs | null;
  consequences: IncidentConsequenceForm[];
  causes: IncidentCauseForm[];
  time: Dayjs | null;
  photos: FileDisplay[];
  severity: Severity | null;
  frequency: Frequency | null;
  risk: Risk | null;
  actions: string;
  advice: string;
  files: FileDisplay[];
}

export interface IncidentCreate {
  group: number;
  name: string;
  description: string;
  location: number;
  involvedPersons: string;
  date: number;
  consequences: IncidentConsequenceCreate[];
  causes: IncidentCauseCreate[];
  severity: Severity;
  frequency: Frequency;
  risk: Risk;
  actions: string;
  advice: string;
  photos: number[];
  files: number[];
}

export interface IncidentUpdate {
  id: number;
  name: string;
  description: string;
  location: number;
  involvedPersons: string;
  date: number;
  consequences: IncidentConsequenceUpdate[];
  causes: IncidentCauseUpdate[];
  severity: Severity;
  frequency: Frequency;
  risk: Risk;
  actions: string;
  advice: string;
  photos: number[];
  files: number[];
}

export interface IncidentRead {
  id: number;
  sid: number;
  group: GroupDisplay;
  name: string;
  description: string;
  location: LocationDisplay;
  involvedPersons: string;
  date: number;
  consequences: IncidentConsequenceRead[];
  causes: IncidentCauseRead[];
  severity: Severity;
  frequency: Frequency;
  risk: Risk;
  actions: string;
  advice: string;
  photos: FileDisplay[];
  files: FileDisplay[];
  creationDate: number;
  createdBy: UserDisplay;
  modifiedDate: number;
  modifiedBy: UserDisplay;
  processInstanceId: string;
  locked: boolean;
  status: IncidentStatus;
}

export interface IncidentListRead {
  id: number;
  sid: number;
  group: GroupDisplay;
  name: string;
  location: LocationDisplay;
  date: number;
  severity: Severity;
  frequency: Frequency;
  risk: Risk;
  locked: boolean;
  status: IncidentStatus;
}

export interface IncidentDeletable {
  deletable: boolean;
}

export interface IncidentChange {
  by: UserDisplay;
  at: number;
  type: IncidentChangeType;
  oldEntity: IncidentRead;
  newEntity: IncidentRead;
}

export enum IncidentChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const IncidentChangeTypeDisplayMap: Record<IncidentChangeType, string> = {
  INSERT: 'Incident reported',
  UPDATE: 'Incident updated',
  DELETE: 'Incident deleted',
};

export interface IncidentState {
  incidentViewState: IncidentViewState;
}

export enum IncidentColumn {
  SID = 'sid',
  TITLE = 'name',
  SEVERITY = 'severity',
  RISK = 'risk',
  GROUP = 'group',
  LOCATION = 'location',
  STATUS = 'status',
  DATE = 'date',
}

export const IncidentColumnDisplayMap: Record<IncidentColumn, string> = {
  [IncidentColumn.SID]: 'ID',
  [IncidentColumn.TITLE]: 'Title',
  [IncidentColumn.SEVERITY]: 'Severity',
  [IncidentColumn.RISK]: 'Risk',
  [IncidentColumn.GROUP]: 'Reported by',
  [IncidentColumn.LOCATION]: 'Location',
  [IncidentColumn.STATUS]: 'Status',
  [IncidentColumn.DATE]: 'Date',
};

export enum IncidentSortField {
  SID = 'sid',
  TITLE = 'name',
  SEVERITY = 'severity',
  DATE = 'date',
  STATUS = 'status',
}

export const IncidentFieldSortMap: Partial<Record<keyof IncidentRead, IncidentSortField>> = {
  sid: IncidentSortField.SID,
  name: IncidentSortField.TITLE,
  severity: IncidentSortField.SEVERITY,
  date: IncidentSortField.DATE,
  status: IncidentSortField.STATUS,
};

export interface IncidentSort {
  field: IncidentSortField;
  direction: 'asc' | 'desc';
}

export interface IncidentColumnSetting {
  column: IncidentColumn;
  hidden?: boolean;
  width?: number;
}

export const IncidentColumnDefaults: IncidentColumnSetting[] = [
  {
    column: IncidentColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: IncidentColumn.TITLE,
    hidden: false,
    width: 300,
  },
  {
    column: IncidentColumn.SEVERITY,
    hidden: false,
    width: 130,
  },
  {
    column: IncidentColumn.RISK,
    hidden: false,
    width: 130,
  },
  {
    column: IncidentColumn.GROUP,
    hidden: false,
    width: 190,
  },
  {
    column: IncidentColumn.LOCATION,
    hidden: false,
    width: 190,
  },
  {
    column: IncidentColumn.STATUS,
    hidden: false,
    width: 130,
  },
  {
    column: IncidentColumn.DATE,
    hidden: false,
    width: 200,
  },
];

export enum IncidentGroupBy {
  GROUP = 'INCIDENTS_GROUP',
  REPORTED_BY = 'INCIDENTS_REPORTED_BY',
  CAUSE = 'INCIDENTS_CAUSE',
  CONSEQUENCE = 'INCIDENTS_CONSEQUENCE',
  SEVERITY = 'INCIDENTS_SEVERITY',
  FREQUENCY = 'INCIDENTS_FREQUENCY',
  RISK = 'INCIDENTS_RISK',
  STATUS = 'INCIDENTS_STATUS',
  LOCATION = 'INCIDENTS_LOCATION',
  ROOT_LOCATION = 'INCIDENTS_ROOT_LOCATION',
  CREATION_DATE_WEEK = 'INCIDENTS_CREATION_DATE_WEEK',
}

export const IncidentGroupByDisplayMap: Record<IncidentGroupBy, string> = {
  [IncidentGroupBy.SEVERITY]: 'Severity',
  [IncidentGroupBy.RISK]: 'Risk',
  [IncidentGroupBy.FREQUENCY]: 'Likelihood',
  [IncidentGroupBy.STATUS]: 'Status',
  [IncidentGroupBy.CONSEQUENCE]: 'Consequences',
  [IncidentGroupBy.CAUSE]: 'Causes',
  [IncidentGroupBy.GROUP]: 'Group',
  [IncidentGroupBy.REPORTED_BY]: 'User',
  [IncidentGroupBy.LOCATION]: 'Location',
  [IncidentGroupBy.ROOT_LOCATION]: 'Root Location',
  [IncidentGroupBy.CREATION_DATE_WEEK]: 'Incident week',
};

export interface IncidentGroupByFieldType {
  [IncidentGroupBy.SEVERITY]: Severity;
  [IncidentGroupBy.RISK]: Risk;
  [IncidentGroupBy.FREQUENCY]: Frequency;
  [IncidentGroupBy.STATUS]: IncidentStatus;
}

export const IncidentGroupByFieldMetaMap: {
  [K in keyof IncidentGroupByFieldType]: EnumMetaMap<IncidentGroupByFieldType[K]>;
} = {
  [IncidentGroupBy.SEVERITY]: SeverityMeta,
  [IncidentGroupBy.RISK]: RiskMeta,
  [IncidentGroupBy.FREQUENCY]: FrequencyMeta,
  [IncidentGroupBy.STATUS]: IncidentStatusMeta,
};
