import { Tab<PERSON><PERSON>x<PERSON>, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs, ToggleButton, ToggleButtonGroup, Tooltip } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import MapIcon from '@mui/icons-material/Map';
import ViewListIcon from '@mui/icons-material/ViewList';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useGetIncidentsQuery } from './incidentApi';
import {
  IncidentColumn,
  IncidentColumnDefaults,
  IncidentColumnDisplayMap,
  IncidentFieldSortMap,
  IncidentListRead,
  IncidentParams,
  IncidentRead,
  IncidentSort,
  IncidentStatus,
  IncidentStatusDisplayMap,
} from './incidentTypes';
import { LocationDisplay, LocationFilterMode } from '../location/locationTypes';
import { useGetCurrentUserQuery } from '../user/userApi';
import IncidentChipFilter from './IncidentChipFilter';
import IncidentFilterBar from './IncidentFilterBar';
import IncidentMap from './IncidentMap';
import Guard from '../guard/Guard';
import { GuardResult } from '../guard/guardHooks';
import { UserRole } from '../user/userTypes';
import { PermissionType } from '../guard/guardTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import { useAppDispatch, useAppSelector } from '../../store';
import { setIncidentViewState } from './incidentSlice';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import IncidentTitleCell from './cell/IncidentTitleCell';
import IncidentSeverityCell from './cell/IncidentSeverityCell';
import IncidentRiskCell from './cell/IncidentRiskCell';
import LocationCell from '../../components/LocationCell';
import IncidentStatusCell from './cell/IncidentStatusCell';
import IncidentDateCell from './cell/IncidentDateCell';
import { GroupDisplay } from '../group/groupTypes';
import { getDatePlusTimeString } from '../../utils';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getIncidentUrl = (id: number) => `./${id}`;

const getSortFromGridModel = (model: GridSortModel): IncidentSort[] =>
  model
    .map((item) => {
      const mappedField = IncidentFieldSortMap[item.field as keyof IncidentRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is IncidentSort => sort !== null);

const getGridModelFromSort = (sort: IncidentSort[]): GridSortModel =>
  sort
    .map((item) => {
      const gridField = Object.entries(IncidentFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

export const columnDefaults: Record<IncidentColumn, GridColDef<IncidentListRead>> = {
  [IncidentColumn.SID]: {
    field: IncidentColumn.SID,
    headerName: IncidentColumnDisplayMap[IncidentColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getIncidentUrl(params.row.id)),
  },
  [IncidentColumn.TITLE]: {
    field: IncidentColumn.TITLE,
    headerName: IncidentColumnDisplayMap[IncidentColumn.TITLE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(IncidentTitleCell(params), getIncidentUrl(params.row.id)),
  },
  [IncidentColumn.SEVERITY]: {
    field: IncidentColumn.SEVERITY,
    headerName: IncidentColumnDisplayMap[IncidentColumn.SEVERITY],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(IncidentSeverityCell(params), getIncidentUrl(params.row.id)),
  },
  [IncidentColumn.RISK]: {
    field: IncidentColumn.RISK,
    headerName: IncidentColumnDisplayMap[IncidentColumn.RISK],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(IncidentRiskCell(params), getIncidentUrl(params.row.id)),
  },
  [IncidentColumn.GROUP]: {
    field: IncidentColumn.GROUP,
    headerName: IncidentColumnDisplayMap[IncidentColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getIncidentUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [IncidentColumn.LOCATION]: {
    field: IncidentColumn.LOCATION,
    headerName: IncidentColumnDisplayMap[IncidentColumn.LOCATION],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(LocationCell(params), getIncidentUrl(params.row.id)),
    valueGetter: (value: LocationDisplay) => (value ? value.name : ''),
  },
  [IncidentColumn.STATUS]: {
    field: IncidentColumn.STATUS,
    headerName: IncidentColumnDisplayMap[IncidentColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, string, string>) =>
      DataGridCellLinkWrapper(IncidentStatusCell(params), getIncidentUrl(params.row.id)),
    valueGetter: (value: IncidentStatus) => (value ? IncidentStatusDisplayMap[value] : ''),
  },
  [IncidentColumn.DATE]: {
    field: IncidentColumn.DATE,
    headerName: IncidentColumnDisplayMap[IncidentColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<IncidentListRead, Date | undefined, string>) =>
      DataGridCellLinkWrapper(IncidentDateCell(params), getIncidentUrl(params.row.id)),
    valueGetter: (value: number | undefined) => (value == null ? undefined : new Date(value)),
    valueFormatter: (value: Date | undefined) => {
      if (!value) {
        return '';
      }
      return getDatePlusTimeString(value);
    },
  },
};

function IncidentListPage() {
  const { groupId } = useParams();
  const { data: me } = useGetCurrentUserQuery();
  const incidentViewState = useAppSelector((state) => state.incident.incidentViewState);
  const dispatch = useAppDispatch();
  const defaultPageSize = 25;

  const { page, setPage, pageSize, setPageSize } = usePaging();
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(incidentViewState?.sort || []));

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || incidentViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }

    return `statusNot=${IncidentStatus.RESOLVED}&statusNot=${IncidentStatus.CANCELED}`;
  };

  const [queryParams, setQueryParams] = useState<IncidentParams>({
    ancestorGroupId: Number(groupId),
    pageSize: defaultPageSize,
    pageNumber: 0,
    search: incidentViewState.search,
    severity: incidentViewState.severity,
    risk: incidentViewState.risk,
    groupId: incidentViewState.group?.id,
    status: incidentViewState.status,
    createdBy: incidentViewState.createdBy,
    filter: getFilter(),
    candidateGroups: incidentViewState?.candidateGroups,
    sort: incidentViewState.sort,
    locationId:
      incidentViewState?.locationFilterMode === LocationFilterMode.EQUALS ? incidentViewState?.location?.id : undefined,
    ancestorLocationId:
      incidentViewState?.locationFilterMode === LocationFilterMode.UNDER ? incidentViewState?.location?.id : undefined,
  });
  const { data, isLoading, error } = useGetIncidentsQuery(queryParams);
  const canReportIncident = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.INCIDENT_CREATE);

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newViewMode: 'table' | 'map' | null) => {
    if (newViewMode !== null) {
      // Update Redux state directly
      dispatch(setIncidentViewState({ ...incidentViewState, viewMode: newViewMode }));
    }
  };

  useEffect(() => {
    if (incidentViewState) {
      setQueryParams((prev) => ({
        ...prev,
        search: incidentViewState.search,
        severity: incidentViewState.severity,
        risk: incidentViewState.risk,
        groupId: incidentViewState.group?.id,
        status: incidentViewState.status,
        createdBy: incidentViewState.createdBy,
        candidateGroups: incidentViewState?.candidateGroups,
        sort: incidentViewState.sort,
        locationId:
          incidentViewState?.locationFilterMode === LocationFilterMode.EQUALS
            ? incidentViewState?.location?.id
            : undefined,
        ancestorLocationId:
          incidentViewState?.locationFilterMode === LocationFilterMode.UNDER
            ? incidentViewState?.location?.id
            : undefined,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [incidentViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    dispatch(
      setIncidentViewState({
        ...incidentViewState,
        listView: view,
      })
    );
    const newParams = { ...queryParams };
    newParams.pageNumber = 0;
    newParams.filter = getFilter(view);

    // Set location parameters directly
    newParams.locationId =
      incidentViewState?.locationFilterMode === LocationFilterMode.EQUALS ? incidentViewState?.location?.id : undefined;
    newParams.ancestorLocationId =
      incidentViewState?.locationFilterMode === LocationFilterMode.UNDER ? incidentViewState?.location?.id : undefined;

    setQueryParams(newParams);
  };

  const columns = useMemo(() => {
    const cols = incidentViewState.columns ? incidentViewState.columns : IncidentColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [incidentViewState.columns]);

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setIncidentViewState({ ...incidentViewState, sort: getSortFromGridModel(newModel) }));
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Incidents" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={incidentViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Box display="flex" gap={2}>
            <ToggleButtonGroup
              value={incidentViewState.viewMode || 'table'}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
              size="small"
            >
              <ToggleButton value="table" aria-label="table view">
                <Tooltip title="Table View">
                  <ViewListIcon />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="map" aria-label="map view">
                <Tooltip title="Map View">
                  <MapIcon />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <Guard hasAccess={canReportIncident}>
              <ResponsiveButton component={Link} to="report" variant="contained" size="large" endIcon={<AddIcon />}>
                Report incident
              </ResponsiveButton>
            </Guard>
          </Box>
        </Box>
        <IncidentChipFilter createdBy={me?.id} resetPageNumber={resetPageNumber} />
        <IncidentFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel sx={{ px: 0, pt: 1, pb: 0 }} value="0">
          {(incidentViewState.viewMode || 'table') === 'table' ? (
            <Paper elevation={4}>
              <Box
                sx={{
                  height: 'calc(100vh - 269px)',
                  overflow: 'hidden',
                  '@media (max-height: 600px)': {
                    height: '100%',
                  },
                }}
              >
                <DataGrid
                  rows={data?.content || []}
                  columns={columns}
                  rowCount={data?.total || 0}
                  loading={isLoading}
                  disableColumnMenu
                  pagination
                  paginationMode="server"
                  paginationModel={{ page, pageSize }}
                  onPaginationModelChange={handlePaginationChange}
                  sortingMode="server"
                  sortModel={sortModel}
                  onSortModelChange={handleSortModelChange}
                  disableRowSelectionOnClick
                  slots={{
                    noRowsOverlay: NoRowsOverlay,
                  }}
                  onColumnWidthChange={(params) => {
                    const newViewState = { ...incidentViewState };
                    if (newViewState.columns) {
                      const updatedColumns = [...newViewState.columns];
                      const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                      if (columnToUpdate) {
                        const index = updatedColumns.indexOf(columnToUpdate);
                        updatedColumns[index] = { ...columnToUpdate, width: params.width };
                      }
                      newViewState.columns = updatedColumns;
                    }
                    dispatch(setIncidentViewState(newViewState));
                  }}
                  slotProps={{
                    loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                    noRowsOverlay: { title: 'No incidents found' },
                  }}
                />
              </Box>
            </Paper>
          ) : (
            <IncidentMap groupId={groupId || ''} params={queryParams} listView={incidentViewState.listView} />
          )}
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default IncidentListPage;
