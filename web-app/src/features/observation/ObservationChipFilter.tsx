import { Checkbox, Chip, FormControlLabel, IconButton, Popover, Stack, Tooltip, Typography } from '@mui/material';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import TuneIcon from '@mui/icons-material/Tune';
import { useState, useEffect } from 'react';
import {
  ObservationCandidateGroupDisplayMap,
  ObservationCandidateGroups,
  ObservationColumn,
  ObservationColumnDefaults,
  ObservationColumnDisplayMap,
  ObservationColumnSetting,
} from './observationTypes';
import { useAppDispatch, useAppSelector } from '../../store';
import { setObservationViewState } from './observationSlice';
import { User } from '../user/userTypes';

interface ObservationChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
}

const mergeColumns = (
  persistedColumns: ObservationColumnSetting[] = [],
  defaultColumns: ObservationColumnSetting[] = []
): ObservationColumnSetting[] => {
  const validPersisted = persistedColumns.filter((pc) => defaultColumns.some((dc) => dc.column === pc.column));
  const persistedSet = new Set(validPersisted.map((pc) => pc.column));
  const newDefaults = defaultColumns.filter((dc) => !persistedSet.has(dc.column));
  return [...validPersisted, ...newDefaults];
};

function ObservationChipFilter({ me, resetPageNumber }: ObservationChipFilterProps) {
  const observationViewState = useAppSelector((state) => state.observation.observationViewState);
  const dispatch = useAppDispatch();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

  const initialColumnsOrder = mergeColumns(observationViewState?.columns, ObservationColumnDefaults);
  const [columnsOrder, setColumnsOrder] = useState<ObservationColumnSetting[]>(initialColumnsOrder);

  useEffect(() => {
    if (observationViewState?.columns) {
      const mergedColumns = mergeColumns(observationViewState.columns, ObservationColumnDefaults);
      setColumnsOrder(mergedColumns);
      if (JSON.stringify(mergedColumns) !== JSON.stringify(observationViewState.columns)) {
        dispatch(setObservationViewState({ ...observationViewState, columns: mergedColumns }));
      }
    }
  }, [dispatch, observationViewState, observationViewState.columns]);

  const handleFineTuneClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      dispatch(setObservationViewState({ ...observationViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: ObservationColumn) => {
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setObservationViewState({ ...observationViewState, columns: newOrder }));
  };

  const open = Boolean(anchorEl);
  const id = open ? 'fine-tune-popover' : undefined;

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <Chip
        label="Reported by me"
        sx={{ mb: 1, mr: 1 }}
        color={observationViewState.createdBy ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...observationViewState };
          if (newState.createdBy) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setObservationViewState(newState));
          resetPageNumber();
        }}
      />
      <Chip
        label="Assigned to me"
        sx={{ mb: 1, mr: 1 }}
        color={
          observationViewState.assignedUser && observationViewState.assignedUser.id === me?.id ? 'primary' : 'default'
        }
        onClick={() => {
          const newState = { ...observationViewState };
          if (newState.assignedUser && newState.assignedUser.id === me?.id) {
            newState.assignedUser = undefined;
          } else {
            newState.assignedUser = me;
          }
          dispatch(setObservationViewState(newState));
          resetPageNumber();
        }}
      />

      {Object.values(ObservationCandidateGroups).map((g) => (
        <Chip
          sx={{ mb: 1, mr: 1 }}
          label={ObservationCandidateGroupDisplayMap[g]}
          color={observationViewState.candidateGroups?.find((c) => c === g) ? 'primary' : 'default'}
          onClick={() => {
            const newState = { ...observationViewState };
            if (!!newState.candidateGroups && !!newState.candidateGroups.find((c) => c === g)) {
              newState.candidateGroups = newState.candidateGroups.filter((c) => c !== g);
            } else {
              const newGroups = newState.candidateGroups || [];
              newState.candidateGroups = newGroups.concat(g);
            }
            dispatch(setObservationViewState(newState));
            resetPageNumber();
          }}
        />
      ))}
      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleFineTuneClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              px: 2,
              py: 1,
              maxWidth: 300,
            },
          },
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={ObservationColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default ObservationChipFilter;
