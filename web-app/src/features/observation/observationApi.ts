import { api, buildInvalidatesTags, buildProvidesTags } from '../../api';
import { PaginatedResult } from '../../utils';
import {
  ObservationScoreRead,
  ObservationScoreCreate,
  ObservationScoreUpdate,
  ObservationScoreParams,
  ObservationChange,
  ObservationScorePDFParams,
} from './observationTypes';

export const observationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getObservations: builder.query<PaginatedResult<ObservationScoreRead>, ObservationScoreParams>({
      query: (params) => {
        const sort =
          params.sort && params.sort.length > 0 ? params.sort.map((s) => `${s.field}:${s.direction}`) : undefined;

        return {
          url: 'safety-walks/observations',
          params: {
            ...params,
            sort,
          },
        };
      },
      providesTags: (result) => buildProvidesTags({ rootTag: 'Observation', response: result?.content }),
    }),
    getObservation: builder.query<ObservationScoreRead, number>({
      query: (id) => `safety-walks/observations/${id}`,
      providesTags: (result) => buildProvidesTags({ rootTag: 'Observation', response: result?.id }),
    }),
    createObservation: builder.mutation<ObservationScoreRead, ObservationScoreCreate>({
      query: (body) => ({
        url: 'safety-walks/observations',
        method: 'POST',
        body,
      }),
      invalidatesTags: () =>
        buildInvalidatesTags({ rootTag: 'Observation', dependentTypes: ['SafetyWalk', 'Task', 'Group', 'User'] }),
    }),
    updateObservation: builder.mutation<ObservationScoreRead, ObservationScoreUpdate>({
      query: (body) => {
        const { id, ...observationUpdate } = body;
        return {
          url: `safety-walks/observations/${id}`,
          method: 'PUT',
          body: observationUpdate,
        };
      },
      invalidatesTags: (result) =>
        buildInvalidatesTags({
          rootTag: 'Observation',
          id: result?.id,
          dependentTypes: ['SafetyWalk', 'Task', 'Group', 'User'],
        }),
    }),
    getObservationHistory: builder.query<ObservationChange[], number>({
      query: (id) => `safety-walks/observations/${id}/history`,
      providesTags: (_result, _error, params) => buildProvidesTags({ rootTag: 'Observation', response: params }),
    }),
    cancelObservation: builder.mutation<void, number>({
      query: (id) => ({
        url: `safety-walks/observations/${id}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, arg) =>
        buildInvalidatesTags({ rootTag: 'Observation', id: arg, dependentTypes: ['Task'] }),
    }),
    downloadObservationPdf: builder.mutation<void, ObservationScorePDFParams>({
      query: (paramsWithId) => {
        const { id, ...params } = paramsWithId;
        return {
          url: `safety-walks/observations/${id}/pdf`,
          params,
          method: 'GET',
          cache: 'default',
          responseHandler: async (response) => {
            const hiddenElement = document.createElement('a');
            hiddenElement.target = '_blank';
            hiddenElement.href = window.URL.createObjectURL(await response.blob());
            hiddenElement.click();
          },
        };
      },
    }),
  }),
});

export const {
  useGetObservationQuery,
  useGetObservationsQuery,
  useUpdateObservationMutation,
  useCreateObservationMutation,
  useGetObservationHistoryQuery,
  useCancelObservationMutation,
  useDownloadObservationPdfMutation,
} = observationApi;
