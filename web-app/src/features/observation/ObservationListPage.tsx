import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs, Tooltip, ToggleButton, ToggleButtonGroup } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import MapIcon from '@mui/icons-material/Map';
import ViewListIcon from '@mui/icons-material/ViewList';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { GridSortModel, GridColDef, GridRenderCellParams, DataGrid, GridPaginationModel } from '@mui/x-data-grid';
import { useGetObservationsQuery } from './observationApi';
import {
  ObservationScoreParams,
  ObservationStatus,
  ObservationStatusDisplayMap,
  ObservationColumn,
  ObservationColumnDisplayMap,
  ObservationFieldSortMap,
  ObservationSort,
  ObservationScoreRead,
  ObservationColumnDefaults,
} from './observationTypes';
import { LocationFilterMode } from '../location/locationTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import ObservationFilterBar from './ObservationFilterBar';
import { PermissionType } from '../guard/guardTypes';
import { GuardResult } from '../guard/guardHooks';
import { UserDisplay, UserRole } from '../user/userTypes';
import Guard from '../guard/Guard';
import ObservationChipFilter from './ObservationChipFilter';
import { useGetCurrentUserQuery } from '../user/userApi';
import { useAppDispatch, useAppSelector } from '../../store';
import { setObservationViewState } from './observationSlice';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import { getDatePlusTimeString } from '../../utils';
import { GroupDisplay } from '../group/groupTypes';
import ObservationDescriptionCell from './cell/ObservationDescriptionCell';
import ObservationScoreCell from './cell/ObservationScoreCell';
import LocationCell from '../../components/LocationCell';
import UserCell from '../../components/UserCell';
import ObservationStatusCell from './cell/ObservationStatusCell';
import ObservationDateCell from './cell/ObservationDateCell';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';
import ObservationMap from './ObservationMap';

const getObservationUrl = (id: number) => `./${id}`;

const getSortFromGridModel = (model: GridSortModel): ObservationSort[] =>
  model
    .map((item) => {
      const mappedField = ObservationFieldSortMap[item.field as keyof typeof ObservationFieldSortMap];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is ObservationSort => sort !== null);

const getGridModelFromSort = (sort: ObservationSort[]): GridSortModel =>
  sort
    .map((item) => {
      const gridField = Object.entries(ObservationFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

export const columnDefaults: Record<ObservationColumn, GridColDef<ObservationScoreRead>> = {
  [ObservationColumn.SID]: {
    field: ObservationColumn.SID,
    headerName: ObservationColumnDisplayMap[ObservationColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getObservationUrl(params.row.id)),
    valueGetter: (_value, row) => (row ? row.observation.sid : ''),
  },
  [ObservationColumn.DESCRIPTION]: {
    field: ObservationColumn.DESCRIPTION,
    headerName: ObservationColumnDisplayMap[ObservationColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(ObservationDescriptionCell(params), getObservationUrl(params.row.id)),
    valueGetter: (_value, row) => (row ? row.observation.description : ''),
  },
  [ObservationColumn.SCORE]: {
    field: ObservationColumn.SCORE,
    headerName: ObservationColumnDisplayMap[ObservationColumn.SCORE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(ObservationScoreCell(params), getObservationUrl(params.row.id)),
  },
  [ObservationColumn.GROUP]: {
    field: ObservationColumn.GROUP,
    headerName: ObservationColumnDisplayMap[ObservationColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getObservationUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [ObservationColumn.ASSIGNED_GROUP]: {
    field: ObservationColumn.ASSIGNED_GROUP,
    headerName: ObservationColumnDisplayMap[ObservationColumn.ASSIGNED_GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getObservationUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [ObservationColumn.ASSIGNED_USER]: {
    field: ObservationColumn.ASSIGNED_USER,
    headerName: ObservationColumnDisplayMap[ObservationColumn.ASSIGNED_USER],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getObservationUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [ObservationColumn.LOCATION]: {
    field: ObservationColumn.LOCATION,
    headerName: ObservationColumnDisplayMap[ObservationColumn.LOCATION],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(LocationCell(params), getObservationUrl(params.row.id)),
    valueGetter: (_value, row) => (row ? row.observation.location.name : ''),
  },
  [ObservationColumn.STATUS]: {
    field: ObservationColumn.STATUS,
    headerName: ObservationColumnDisplayMap[ObservationColumn.STATUS],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, string, string>) =>
      DataGridCellLinkWrapper(ObservationStatusCell(params), getObservationUrl(params.row.id)),
    valueGetter: (value: ObservationStatus) => (value ? ObservationStatusDisplayMap[value] : ''),
  },
  [ObservationColumn.DATE]: {
    field: ObservationColumn.DATE,
    headerName: ObservationColumnDisplayMap[ObservationColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ObservationScoreRead, Date | undefined, string>) =>
      DataGridCellLinkWrapper(ObservationDateCell(params), getObservationUrl(params.row.id)),
    valueGetter: (value: number | undefined) => (value == null ? undefined : new Date(value)),
    valueFormatter: (value: Date | undefined) => {
      if (!value) {
        return '';
      }
      return getDatePlusTimeString(value);
    },
  },
};

function ObservationListPage() {
  const { groupId } = useParams();
  const { data: me } = useGetCurrentUserQuery();
  const observationViewState = useAppSelector((state) => state.observation.observationViewState);
  const dispatch = useAppDispatch();
  const defaultPageSize = 25;

  const { page, setPage, pageSize, setPageSize } = usePaging();
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(observationViewState?.sort || []));

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || observationViewState.listView;
    if (usedView === 'all') {
      return `(ancestorId=${groupId}|ancestorAssignedGroupId=${groupId})`;
    }
    return `
    statusNot=${ObservationStatus.RESOLVED}&statusNot=${ObservationStatus.CANCELED}
    &
    (ancestorId=${groupId}|ancestorAssignedGroupId=${groupId})
  `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [queryParams, setQueryParams] = useState<ObservationScoreParams>({
    pageSize: defaultPageSize,
    pageNumber: 0,
    groupId: observationViewState.group?.id,
    assignedGroup: observationViewState.assignedGroup?.id,
    status: observationViewState.status,
    createdBy: observationViewState.createdBy?.id,
    assignedUser: observationViewState.assignedUser?.id,
    search: observationViewState.search,
    score: observationViewState.score,
    candidateGroups: observationViewState.candidateGroups,
    sort: observationViewState.sort,
    filter: getFilter(),
    locationId:
      observationViewState?.locationFilterMode === LocationFilterMode.EQUALS
        ? observationViewState?.location?.id
        : undefined,
    ancestorLocationId:
      observationViewState?.locationFilterMode === LocationFilterMode.UNDER
        ? observationViewState?.location?.id
        : undefined,
  });

  const { data, isLoading, error } = useGetObservationsQuery(queryParams);

  const canCreateObservation = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.OBSERVATION_CREATE);

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newViewMode: 'table' | 'map' | null) => {
    if (newViewMode !== null) {
      // Update Redux state directly
      dispatch(setObservationViewState({ ...observationViewState, viewMode: newViewMode }));
    }
  };

  useEffect(() => {
    if (observationViewState) {
      setQueryParams((prev) => ({
        ...prev,
        groupId: observationViewState.group?.id,
        assignedGroup: observationViewState.assignedGroup?.id,
        status: observationViewState.status,
        createdBy: observationViewState.createdBy?.id,
        assignedUser: observationViewState.assignedUser?.id,
        search: observationViewState.search,
        score: observationViewState.score,
        candidateGroups: observationViewState.candidateGroups,
        sort: observationViewState.sort,
        locationId:
          observationViewState?.locationFilterMode === LocationFilterMode.EQUALS
            ? observationViewState?.location?.id
            : undefined,
        ancestorLocationId:
          observationViewState?.locationFilterMode === LocationFilterMode.UNDER
            ? observationViewState?.location?.id
            : undefined,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [observationViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    dispatch(
      setObservationViewState({
        ...observationViewState,
        listView: view,
      })
    );
    const newParams = { ...queryParams };
    newParams.pageNumber = 0;
    newParams.filter = getFilter(view);

    // Set location parameters directly
    newParams.locationId =
      observationViewState?.locationFilterMode === LocationFilterMode.EQUALS
        ? observationViewState?.location?.id
        : undefined;
    newParams.ancestorLocationId =
      observationViewState?.locationFilterMode === LocationFilterMode.UNDER
        ? observationViewState?.location?.id
        : undefined;

    setQueryParams(newParams);
  };

  const columns = useMemo(() => {
    const cols = observationViewState.columns ? observationViewState.columns : ObservationColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [observationViewState.columns]);

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setObservationViewState({ ...observationViewState, sort: getSortFromGridModel(newModel) }));
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Observations" />
      <TabContext value="0">
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Tabs value={observationViewState.listView}>
              <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
              <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
            </Tabs>
            <Box display="flex" gap={2}>
              <ToggleButtonGroup
                value={observationViewState.viewMode || 'table'}
                exclusive
                onChange={handleViewModeChange}
                aria-label="view mode"
                size="small"
              >
                <ToggleButton value="table" aria-label="table view">
                  <Tooltip title="Table View">
                    <ViewListIcon />
                  </Tooltip>
                </ToggleButton>
                <ToggleButton value="map" aria-label="map view">
                  <Tooltip title="Map View">
                    <MapIcon />
                  </Tooltip>
                </ToggleButton>
              </ToggleButtonGroup>
              <Guard hasAccess={canCreateObservation}>
                <ResponsiveButton component={Link} to="report" variant="contained" size="large" endIcon={<AddIcon />}>
                  Report observation
                </ResponsiveButton>
              </Guard>
            </Box>
          </Box>

          <ObservationChipFilter me={me} resetPageNumber={resetPageNumber} />
          <ObservationFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        </Box>
        <TabPanel sx={{ px: 0, pt: 1, pb: 0 }} value="0">
          {(observationViewState.viewMode || 'table') === 'table' ? (
            <Paper elevation={4}>
              <Box
                sx={{
                  height: 'calc(100vh - 269px)',
                  overflow: 'hidden',
                  '@media (max-height: 600px)': {
                    height: '100%',
                  },
                }}
              >
                <DataGrid
                  rows={data?.content || []}
                  columns={columns}
                  rowCount={data?.total || 0}
                  loading={isLoading}
                  disableColumnMenu
                  pagination
                  paginationMode="server"
                  paginationModel={{ page, pageSize }}
                  onPaginationModelChange={handlePaginationChange}
                  sortingMode="server"
                  sortModel={sortModel}
                  onSortModelChange={handleSortModelChange}
                  disableRowSelectionOnClick
                  slots={{
                    noRowsOverlay: NoRowsOverlay,
                  }}
                  onColumnWidthChange={(params) => {
                    const newViewState = { ...observationViewState };
                    if (newViewState.columns) {
                      const updatedColumns = [...newViewState.columns];
                      const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                      if (columnToUpdate) {
                        const index = updatedColumns.indexOf(columnToUpdate);
                        updatedColumns[index] = { ...columnToUpdate, width: params.width };
                      }
                      newViewState.columns = updatedColumns;
                    }
                    dispatch(setObservationViewState(newViewState));
                  }}
                  slotProps={{
                    loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                    noRowsOverlay: { title: 'No observations found' },
                  }}
                />
              </Box>
            </Paper>
          ) : (
            <ObservationMap groupId={groupId || ''} params={queryParams} listView={observationViewState.listView} />
          )}
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ObservationListPage;
