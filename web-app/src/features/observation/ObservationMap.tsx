import { Box, Skeleton, TableContainer, Paper, Alert, IconButton, Tooltip } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useMemo, useCallback, useEffect, useState } from 'react';
import MapViewer from '../location/viewer/MapViewer';
import AutodeskViewer from '../location/viewer/AutodeskViewer';
import { KeyMetadata, StatsGroupBy, StatsModule, CountObject, StatsKeyValue } from '../stats/statsTypes';
import { useGetStatsQuery } from '../stats/statsApi';
import { useGetLocationQuery } from '../location/locationApi';
import { useAppDispatch, useAppSelector } from '../../store';
import { setObservationViewState } from './observationSlice';
import { ObservationScoreParams, ObservationStatus } from './observationTypes';
import { LocationFilterMode } from '../location/locationTypes';

interface ObservationMapProps {
  groupId: string | number;
  params: ObservationScoreParams;
  listView: 'mine' | 'all';
}

function ObservationMap({ groupId, params, listView }: ObservationMapProps) {
  const dispatch = useAppDispatch();
  const observationViewState = useAppSelector((state) => state.observation.observationViewState);

  // Stats query for map view - uses location coordinates from metadata to place markers
  const statsParams = useMemo(() => {
    // Build filter string from params
    const filterParts: string[] = [];

    // Use pathGroupId as the primary filter to match ObservationListPage
    filterParts.push(`(ancestorId=${groupId}|ancestorAssignedGroupId=${groupId})`);

    // Add status filter based on listView
    if (listView === 'mine') {
      // For 'mine' view, exclude resolved and canceled observations
      filterParts.push(`statusNot=${ObservationStatus.RESOLVED}`);
      filterParts.push(`statusNot=${ObservationStatus.CANCELED}`);
    }

    if (params.groupId) filterParts.push(`groupId=${params.groupId}`);
    if (params.status) filterParts.push(`status=${params.status}`);
    if (params.createdBy) filterParts.push(`createdBy=${params.createdBy}`);
    if (params.search) filterParts.push(`search=${params.search}`);
    if (params.score) filterParts.push(`score=${params.score}`);
    if (params.assignedGroup) filterParts.push(`assignedGroup=${params.assignedGroup}`);
    if (params.assignedUser) filterParts.push(`assignedUser=${params.assignedUser}`);

    // If we have a selected location, use it to filter the statistics
    if (params.locationId) {
      filterParts.push(`locationId=${params.locationId}`);
    } else if (params.ancestorLocationId) {
      filterParts.push(`ancestorLocationId=${params.ancestorLocationId}`);
    }

    // Handle candidate groups with proper OR logic
    if (params.candidateGroups?.length) {
      const candidateGroupFilters = params.candidateGroups.map((group) => `candidateGroups=${group}`);

      if (candidateGroupFilters.length > 1) {
        filterParts.push(`(${candidateGroupFilters.join('|')})`);
      } else {
        filterParts.push(candidateGroupFilters[0]);
      }
    }

    // When a location is selected, use OBSERVATIONS_LOCATION for detailed breakdown
    // Otherwise use OBSERVATIONS_ROOT_LOCATION for the overview map
    const groupByParam = observationViewState.location
      ? StatsGroupBy.OBSERVATIONS_LOCATION
      : StatsGroupBy.OBSERVATIONS_ROOT_LOCATION;

    return {
      // Use pathGroupId to match the list page parameter
      ancestorGroupId: Number(groupId),
      module: StatsModule.OBSERVATIONS,
      groupBy: [groupByParam],
      filter: filterParts.join('&'),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  }, [groupId, params, listView, observationViewState.location]);

  const { data: statsData } = useGetStatsQuery(statsParams);

  // Get the selected location data if a location is selected
  const { data: selectedLocation, isLoading: isLocationLoading } = useGetLocationQuery(
    observationViewState.location?.id || 0,
    { skip: !observationViewState.location?.id }
  );

  // Define types for stats data structure
  interface Position {
    x: number;
    y: number;
    z: number;
  }

  interface PositionItem {
    position: Position;
    count: number;
    locationId?: number;
    locationName: string;
  }

  // Transform stats data for map display
  const mapPositions = useMemo(() => {
    if (!statsData) return [];

    const positions: PositionItem[] = [];

    // Helper function to create position item
    const createPositionItem = (locationName: string, count: number, metadata?: KeyMetadata): PositionItem => {
      // Extract coordinates and location ID from metadata if available
      const xCoord = typeof metadata?.x_coordinate === 'number' ? metadata.x_coordinate : 0;
      const yCoord = typeof metadata?.y_coordinate === 'number' ? metadata.y_coordinate : 0;
      const zCoord = typeof metadata?.z_coordinate === 'number' ? metadata.z_coordinate : 0;
      const locId = typeof metadata?.location_id === 'number' ? metadata.location_id : undefined;

      return {
        position: { x: xCoord, y: yCoord, z: zCoord },
        count,
        locationId: locId,
        locationName: `${locationName} (${count} observation${count !== 1 ? 's' : ''})`,
      };
    };

    // Process stats data recursively with improved type safety
    const processStats = (statsObj: StatsKeyValue, path: string[] = []): void => {
      Object.entries(statsObj).forEach(([key, value]) => {
        // Skip metadata key
        if (key === '_metadata') return;

        // Current path including this key
        const currentPath = [...path, key];

        // Check if this is a CountObject (has count property)
        if ('count' in value && typeof value.count === 'number') {
          // It's a CountObject with count property
          const countObj = value as CountObject;
          // eslint-disable-next-line no-underscore-dangle
          const metadata = countObj._metadata;

          // For OBSERVATIONS_LOCATION, we might have nested location data
          // The location name should be the last part of the path (the actual location name)
          const displayName = observationViewState.location ? currentPath[currentPath.length - 1] : key;

          positions.push(createPositionItem(displayName, countObj.count, metadata));
        } else {
          // It's a nested StatsKeyValue - process recursively with updated path
          processStats(value as StatsKeyValue, currentPath);
        }
      });
    };

    // Type guard to ensure statsData is a StatsKeyValue
    if (statsData && typeof statsData === 'object') {
      processStats(statsData as StatsKeyValue);
    }

    return positions;
  }, [statsData, observationViewState.location]);

  // Handle location click
  const handleLocationClick = useCallback(
    (locationId?: number) => {
      if (locationId !== undefined) {
        // Find the location in the map positions
        const clickedLocation = mapPositions.find((pos) => pos.locationId === locationId);
        if (clickedLocation) {
          // Store the selected location in the Redux state
          const locationFilterMode = observationViewState.locationFilterMode
            ? observationViewState.locationFilterMode
            : LocationFilterMode.UNDER;
          dispatch(
            setObservationViewState({
              ...observationViewState,
              location: {
                id: locationId,
                name: clickedLocation.locationName.split(' (')[0], // Remove the count part from the name
              },
              locationFilterMode,
            })
          );
        }
      } else {
        // If locationId is undefined, clear the selection
        dispatch(
          setObservationViewState({
            ...observationViewState,
            location: undefined,
            locationFilterMode: undefined,
          })
        );
      }
    },
    [observationViewState, dispatch, mapPositions]
  );

  // Function to go back to the map view
  const handleBackToMap = useCallback(() => {
    dispatch(
      setObservationViewState({
        ...observationViewState,
        location: undefined,
        locationFilterMode: undefined,
      })
    );
  }, [dispatch, observationViewState]);

  // Determine if we should show the map or the Autodesk viewer
  const showAutodeskViewer =
    observationViewState.location !== undefined && selectedLocation !== undefined && selectedLocation.model !== null;

  // State to track if we should show a no-model message
  const [showNoModelMessage, setShowNoModelMessage] = useState(false);

  // Handle the case when a location is selected but doesn't have a model
  useEffect(() => {
    if (observationViewState.location && selectedLocation && !selectedLocation.model) {
      // Show a message that the location doesn't have a model
      setShowNoModelMessage(true);

      // Auto-hide after 3 seconds and return to map view
      const timer = setTimeout(() => {
        setShowNoModelMessage(false);
        dispatch(
          setObservationViewState({
            ...observationViewState,
            location: undefined,
            locationFilterMode: undefined,
          })
        );
      }, 3000);

      return () => clearTimeout(timer);
    }

    setShowNoModelMessage(false);
    return undefined;
  }, [selectedLocation, observationViewState.location, dispatch, observationViewState]);

  return (
    <TableContainer
      component={Paper}
      elevation={4}
      sx={{
        overflow: 'hidden',
        maxWidth: '100%',
        position: 'relative',
      }}
    >
      {/* Show message when location has no model */}
      {showNoModelMessage && (
        <Alert
          severity="info"
          sx={{
            position: 'absolute',
            top: 10,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1100,
            maxWidth: '90%',
            width: 'auto',
            opacity: 0.95,
            borderRadius: 1,
          }}
          onClose={() => {
            setShowNoModelMessage(false);
            dispatch(
              setObservationViewState({
                ...observationViewState,
                location: undefined,
                locationFilterMode: undefined,
              })
            );
          }}
        >
          This location does not have a model. Returning to map view...
        </Alert>
      )}
      {showAutodeskViewer ? (
        // Show Autodesk viewer when a location with a model is selected
        <Box
          id="autodesk-viewer-parent"
          sx={{
            height: 'calc(100vh - 269px)',
            '@media (max-height: 600px)': {
              height: '400px',
            },
            width: '100%',
            position: 'relative',
          }}
        >
          {isLocationLoading ? (
            <Box display="flex" height="100%" alignItems="center" justifyContent="center">
              <Skeleton variant="rectangular" width="100%" height="100%" />
            </Box>
          ) : (
            <>
              <Tooltip title="Back to map">
                <IconButton
                  size="medium"
                  onClick={handleBackToMap}
                  sx={{
                    position: 'absolute',
                    top: 10,
                    left: 10,
                    zIndex: 1000,
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
              <AutodeskViewer
                parentId="autodesk-viewer-parent"
                model={selectedLocation?.model || null}
                sheet={selectedLocation?.sheet || 0}
                // Use all positions from the statistics data that have valid coordinates
                positions={mapPositions.filter((pos) => {
                  // Filter out positions with null, undefined, or zero coordinates
                  const hasValidCoordinates =
                    pos.position &&
                    typeof pos.position.x === 'number' &&
                    typeof pos.position.y === 'number' &&
                    (pos.position.x !== 0 || pos.position.y !== 0 || pos.position.z !== 0);

                  // Make sure we have a location ID and count
                  const hasRequiredData = typeof pos.locationId === 'number' && typeof pos.count === 'number';

                  return hasValidCoordinates && hasRequiredData;
                })}
              />
            </>
          )}
        </Box>
      ) : (
        // Show map view when no location is selected
        <Box
          id="map-viewer-parent"
          sx={{
            height: 'calc(100vh - 269px)',
            '@media (max-height: 600px)': {
              height: '400px',
            },
            width: '100%',
          }}
        >
          {!statsData ? (
            <Box display="flex" height="100%" alignItems="center" justifyContent="center">
              <Skeleton variant="rectangular" width="100%" height="100%" />
            </Box>
          ) : (
            <MapViewer parentId="map-viewer-parent" positions={mapPositions} onLocationClick={handleLocationClick} />
          )}
        </Box>
      )}
    </TableContainer>
  );
}

export default ObservationMap;
