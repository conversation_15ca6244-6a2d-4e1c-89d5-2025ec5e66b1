import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { SafetyWalkColumnDefaults, SafetyWalkState, SafetyWalkViewState } from './safetyWalkTypes';

const initialState: SafetyWalkState = {
  safetyWalkViewState: {
    listView: 'mine',
    columns: SafetyWalkColumnDefaults,
  },
};

export const safetyWalkSlice = createSlice({
  name: 'safetyWalk',
  initialState,
  reducers: {
    setSafetyWalkViewState: (state, action: PayloadAction<SafetyWalkViewState>) => {
      state.safetyWalkViewState = action.payload;
    },
  },
});

export const { setSafetyWalkViewState } = safetyWalkSlice.actions;

export const safetyWalkReducer = persistReducer(
  {
    key: 'safetyWalk',
    storage,
    whitelist: ['safetyWalkViewState'],
  },
  safetyWalkSlice.reducer
);
