import { Dayjs } from 'dayjs';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import {
  ObservationCreate,
  ObservationRead,
  ObservationStatus,
  SafetyWalkScore,
  SafetyWalkScoreMeta,
} from '../observation/observationTypes';
import { User, UserDisplay } from '../user/userTypes';
import { WorkPermitDisplay } from '../work-permit/workPermitTypes';
import { SafetyWalkQuestionDisplay, SafetyWalkTemplateRead } from './safetyWalkTemplateTypes';
import { FileDisplay } from '../file/fileTypes';
import { LocationDisplay } from '../location/locationTypes';
import { EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';

export interface SafetyWalkParams {
  createdBy?: number;
  ancestorGroupId?: number;
  groupId?: number;
  status?: SafetyWalkStatus;
  template?: number;
  search?: string;
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
  sort?: SafetyWalkSort[];
}

export const enum SafetyWalkColumn {
  SID = 'sid',
  TEMPLATE = 'safetyWalkTemplate',
  SUMMARY = 'summary',
  GROUP = 'group',
  REPORTED_USER = 'createdBy',
  STATUS = 'status',
  DATE = 'date',
}

export const SafetyWalkColumnDisplayMap: Record<SafetyWalkColumn, string> = {
  [SafetyWalkColumn.SID]: 'ID',
  [SafetyWalkColumn.TEMPLATE]: 'Template',
  [SafetyWalkColumn.SUMMARY]: 'Summary',
  [SafetyWalkColumn.GROUP]: 'Reported group',
  [SafetyWalkColumn.REPORTED_USER]: 'Reported user',
  [SafetyWalkColumn.STATUS]: 'Status',
  [SafetyWalkColumn.DATE]: 'Date',
};

export interface SafetyWalkColumnSetting {
  column: SafetyWalkColumn;
  hidden: boolean;
  width: number;
}

export const SafetyWalkColumnDefaults: SafetyWalkColumnSetting[] = [
  {
    column: SafetyWalkColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: SafetyWalkColumn.TEMPLATE,
    hidden: false,
    width: 190,
  },
  {
    column: SafetyWalkColumn.SUMMARY,
    hidden: false,
    width: 300,
  },
  {
    column: SafetyWalkColumn.GROUP,
    hidden: false,
    width: 190,
  },
  {
    column: SafetyWalkColumn.REPORTED_USER,
    hidden: false,
    width: 190,
  },
  {
    column: SafetyWalkColumn.STATUS,
    hidden: false,
    width: 130,
  },
  {
    column: SafetyWalkColumn.DATE,
    hidden: false,
    width: 200,
  },
];

export interface SafetyWalkPDFParams {
  id: number;
  timeZone: string;
  withObservationFiles?: boolean;
}

export interface SafetyWalkViewState {
  listView: 'mine' | 'all';
  template?: SafetyWalkTemplateRead;
  group?: GroupListRead;
  status?: SafetyWalkStatus;
  columns: SafetyWalkColumnSetting[];
  sort?: SafetyWalkSort[];
  createdBy?: User;
  search?: string;
}

export interface SafetyWalkRead {
  id: number;
  sid: number;
  processInstanceId: string;
  status: SafetyWalkStatus;
  safetyWalkTemplate: SafetyWalkTemplateRead;
  observations: SafetyWalkObservationScoreRead[];
  group: GroupDisplay;
  date: number;
  createdAt: number;
  modifiedAt: number;
  createdBy: UserDisplay;
  modifiedBy: UserDisplay;
  locked: boolean;
}

export interface SafetyWalkCreate {
  safetyWalkTemplate: number;
  date: number;
  group: number;
  observations: SafetyWalkObservationScoreCreate[];
}

export interface SafetyWalkUpdate {
  id: number;
  date: number;
  observations: SafetyWalkObservationScoreUpdate[];
}

export interface SafetyWalkDeletable {
  deletable: boolean;
}

export interface SafetyWalkObservationScoreCreate {
  question: number;
  score: SafetyWalkScore;
  workPermit: number | null;
  category: number | null;
  cause: number | null;
  observation: ObservationCreate | null;
}

export interface SafetyWalkObservationScoreUpdate {
  id?: number;
  question: number;
  score: SafetyWalkScore;
  workPermit: number | null;
  category: number | null;
  cause: number | null;
  observation: ObservationCreate | null;
}

export interface SafetyWalkObservationScoreRead {
  id: number;
  score: SafetyWalkScore;
  processInstanceId: string;
  status: ObservationStatus;
  workPermit?: WorkPermitDisplay;
  category?: number;
  cause?: number;
  question: SafetyWalkQuestionDisplay;
  observation: ObservationRead | null;
  locked: boolean;
}

export enum SafetyWalkStatus {
  REPORTED = 'REPORTED',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const SafetyWalkStatusMeta: EnumMetaMap<SafetyWalkStatus> = {
  [SafetyWalkStatus.REPORTED]: { label: 'Reported', color: themeToColor('primary.main') },
  [SafetyWalkStatus.RESOLVED]: { label: 'Resolved', color: themeToColor('secondary.main') },
  [SafetyWalkStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export enum SafetyWalkSortField {
  SID = 'sid',
  DATE = 'date',
}

export const SafetyWalkFieldSortMap: Partial<Record<keyof SafetyWalkRead, SafetyWalkSortField>> = {
  sid: SafetyWalkSortField.SID,
  date: SafetyWalkSortField.DATE,
};

export interface SafetyWalkSort {
  field: SafetyWalkSortField;
  direction: 'asc' | 'desc';
}

export const SafetyWalkStatusDisplayMap = Object.fromEntries(
  Object.entries(SafetyWalkStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<SafetyWalkStatus, string>;

export interface SafetyWalkFormInput {
  group: GroupDisplay | null;
  template: SafetyWalkTemplateRead | null;
  date: Dayjs;
  workPermit: WorkPermitDisplay | null;
  observations: SafetyWalkAnswerFormInput[];
}

export interface SafetyWalkAnswerFormInput {
  answerRead?: SafetyWalkObservationScoreRead;
  category: number | null;
  cause: number | null;
  question: SafetyWalkQuestionDisplay;
  score: SafetyWalkScore;
  observation: SafetyWalkObservationFormInput;
}

export interface SafetyWalkObservationFormInput {
  description: string | null;
  location: LocationDisplay | null;
  files: FileDisplay[] | null;
}

export interface SafetyWalkState {
  safetyWalkViewState: SafetyWalkViewState;
}

export enum SafetyWalkChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const SafetyWalkChangeTypeDisplayMap: Record<SafetyWalkChangeType, string> = {
  INSERT: 'Safety walk reported.',
  UPDATE: 'Safety walk updated.',
  DELETE: 'Safety walk deleted.',
};

export interface SafetyWalkChange {
  by: UserDisplay;
  at: number;
  type: SafetyWalkChangeType;
  oldEntity: SafetyWalkRead;
  newEntity: SafetyWalkRead;
}

export enum SafetyWalkGroupBy {
  GROUP = 'SAFETYWALKS_GROUP',
  REPORTED_BY = 'SAFETYWALKS_REPORTED_BY',
  SCORE = 'SAFETYWALKS_SCORE',
  CATEGORY = 'SAFETYWALKS_CATEGORY',
  CAUSE = 'SAFETYWALKS_CAUSE',
  STATUS = 'SAFETYWALKS_STATUS',
  DATE_WEEK = 'SAFETYWALKS_DATE_WEEK',
}

export const SafetyWalkGroupByDisplayMap: Record<SafetyWalkGroupBy, string> = {
  [SafetyWalkGroupBy.GROUP]: 'Group',
  [SafetyWalkGroupBy.REPORTED_BY]: 'User',
  [SafetyWalkGroupBy.SCORE]: 'Score',
  [SafetyWalkGroupBy.CATEGORY]: 'Category',
  [SafetyWalkGroupBy.CAUSE]: 'Cause',
  [SafetyWalkGroupBy.STATUS]: 'Status',
  [SafetyWalkGroupBy.DATE_WEEK]: 'Safety walk week',
};

export interface SafetyWalkGroupByFieldType {
  [SafetyWalkGroupBy.SCORE]: SafetyWalkScore;
  [SafetyWalkGroupBy.STATUS]: SafetyWalkStatus;
}

export const SafetyWalkGroupByFieldMetaMap: {
  [K in keyof SafetyWalkGroupByFieldType]: EnumMetaMap<SafetyWalkGroupByFieldType[K]>;
} = {
  [SafetyWalkGroupBy.SCORE]: SafetyWalkScoreMeta,
  [SafetyWalkGroupBy.STATUS]: SafetyWalkStatusMeta,
};
