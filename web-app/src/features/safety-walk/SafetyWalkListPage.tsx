import { Tab<PERSON><PERSON>x<PERSON>, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import { useGetSafetyWalksQuery } from './safetyWalkApi';
import {
  SafetyWalkColumn,
  SafetyWalkColumnDefaults,
  SafetyWalkColumnDisplayMap,
  SafetyWalkFieldSortMap,
  SafetyWalkParams,
  SafetyWalkRead,
  SafetyWalkSort,
  SafetyWalkStatus,
  SafetyWalkStatusDisplayMap,
} from './safetyWalkTypes';
import SafetyWalkFilterBar from './SafetyWalkFilterBar';
import SafetyWalkChipFilter from './SafetyWalkChipFilter';
import { useGetCurrentUserQuery } from '../user/userApi';
import { GuardResult } from '../guard/guardHooks';
import { UserDisplay, UserRole } from '../user/userTypes';
import { PermissionType } from '../guard/guardTypes';
import Guard from '../guard/Guard';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSafetyWalkViewState } from './safetyWalkSlice';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import SidCell from '../../components/SidCell';
import SafetyWalkTemplateCell from './cell/SafetyWalkTemplateCell';
import { SafetyWalkTemplateRead } from './safetyWalkTemplateTypes';
import SafetyWalkSummaryCell from './cell/SafetyWalkSummaryCell';
import GroupCell from '../../components/GroupCell';
import { GroupDisplay } from '../group/groupTypes';
import UserCell from '../../components/UserCell';
import SafetyWalkStatusCell from './cell/SafetyWalkStatusCell';
import { getDatePlusTimeString } from '../../utils';
import SafetyWalkDateCell from './cell/SafetyWalkDateCell';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getSafetyWalkUrl = (id: number) => `./${id}`;

const getSortFromGridModel = (model: GridSortModel): SafetyWalkSort[] =>
  model
    .map((item) => {
      const mappedField = SafetyWalkFieldSortMap[item.field as keyof SafetyWalkRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is SafetyWalkSort => sort !== null);

const getGridModelFromSort = (sort: SafetyWalkSort[]): GridSortModel =>
  sort
    .map((item) => {
      // Find the grid field whose value in TaskFieldSortMap equals the backend sort field
      const gridField = Object.entries(SafetyWalkFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

export const columnDefaults: Record<SafetyWalkColumn, GridColDef<SafetyWalkRead>> = {
  [SafetyWalkColumn.SID]: {
    field: SafetyWalkColumn.SID,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getSafetyWalkUrl(params.row.id)),
  },
  [SafetyWalkColumn.TEMPLATE]: {
    field: SafetyWalkColumn.TEMPLATE,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.TEMPLATE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(SafetyWalkTemplateCell(params), getSafetyWalkUrl(params.row.id)),
    valueGetter: (value: SafetyWalkTemplateRead) => (value ? value.name : ''),
  },
  [SafetyWalkColumn.SUMMARY]: {
    field: SafetyWalkColumn.SUMMARY,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.SUMMARY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(SafetyWalkSummaryCell(params), getSafetyWalkUrl(params.row.id)),
  },
  [SafetyWalkColumn.GROUP]: {
    field: SafetyWalkColumn.GROUP,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getSafetyWalkUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [SafetyWalkColumn.REPORTED_USER]: {
    field: SafetyWalkColumn.REPORTED_USER,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.REPORTED_USER],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getSafetyWalkUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [SafetyWalkColumn.STATUS]: {
    field: SafetyWalkColumn.STATUS,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, string, string>) =>
      DataGridCellLinkWrapper(SafetyWalkStatusCell(params), getSafetyWalkUrl(params.row.id)),
    valueGetter: (value: SafetyWalkStatus) => (value ? SafetyWalkStatusDisplayMap[value] : ''),
  },
  [SafetyWalkColumn.DATE]: {
    field: SafetyWalkColumn.DATE,
    headerName: SafetyWalkColumnDisplayMap[SafetyWalkColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<SafetyWalkRead, Date | undefined, string>) =>
      DataGridCellLinkWrapper(SafetyWalkDateCell(params), getSafetyWalkUrl(params.row.id)),
    valueGetter: (value: number | undefined) => (value == null ? undefined : new Date(value)),
    valueFormatter: (value: Date | undefined) => {
      if (!value) {
        return '';
      }
      return getDatePlusTimeString(value);
    },
  },
};

function SafetyWalkListPage() {
  const { data: me } = useGetCurrentUserQuery();
  const { groupId } = useParams();
  const safetyWalkViewState = useAppSelector((state) => state.safetyWalk.safetyWalkViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(safetyWalkViewState?.sort || []));
  const { page, setPage, pageSize, setPageSize } = usePaging();
  const dispatch = useAppDispatch();
  const defaultPageSize = 25;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || safetyWalkViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${SafetyWalkStatus.RESOLVED}&statusNot=${SafetyWalkStatus.CANCELED}`;
  };

  const [queryParams, setQueryParams] = useState<SafetyWalkParams>({
    ancestorGroupId: Number(groupId),
    createdBy: safetyWalkViewState.createdBy?.id,
    groupId: safetyWalkViewState.group?.id,
    search: safetyWalkViewState.search,
    template: safetyWalkViewState.template?.id,
    status: safetyWalkViewState?.status,
    sort: safetyWalkViewState?.sort,
    filter: getFilter(),
    pageSize: defaultPageSize,
    pageNumber: 0,
  });
  const { data, isLoading, error } = useGetSafetyWalksQuery(queryParams);
  const canRequestSafetyWalk = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.OBSERVATION_CREATE);
  // ? Avoid a layout jump when reaching the last page with empty rows.

  useEffect(() => {
    if (safetyWalkViewState) {
      setQueryParams((prev) => ({
        ...prev,
        groupId: safetyWalkViewState?.group?.id,
        createdBy: safetyWalkViewState.listView === 'all' ? safetyWalkViewState.createdBy?.id : undefined,
        search: safetyWalkViewState?.search,
        template: safetyWalkViewState?.template?.id,
        status: safetyWalkViewState?.status,
        sort: safetyWalkViewState?.sort,
      }));
    }
  }, [safetyWalkViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    dispatch(
      setSafetyWalkViewState({
        ...safetyWalkViewState,
        listView: view,
      })
    );
    const newParams = { ...queryParams };
    newParams.pageNumber = 0;
    newParams.filter = getFilter(view);
    setQueryParams(newParams);
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setSafetyWalkViewState({ ...safetyWalkViewState, sort: getSortFromGridModel(newModel) }));
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  const columns = useMemo(() => {
    const cols = safetyWalkViewState.columns ? safetyWalkViewState.columns : SafetyWalkColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [safetyWalkViewState.columns]);

  return (
    <ErrorGate error={error}>
      <PageTitle page="Safety walks" />
      <TabContext value="0">
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Tabs value={safetyWalkViewState.listView}>
              <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
              <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
            </Tabs>
            <Guard hasAccess={canRequestSafetyWalk}>
              <ResponsiveButton component={Link} to="report" variant="contained" size="large" endIcon={<AddIcon />}>
                Report safety walk
              </ResponsiveButton>
            </Guard>
          </Box>

          <SafetyWalkChipFilter me={me} resetPageNumber={resetPageNumber} />
          <SafetyWalkFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        </Box>
        <TabPanel
          value="0"
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...safetyWalkViewState };
                  if (newViewState.columns) {
                    const updatedColumns = [...newViewState.columns];
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setSafetyWalkViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No safety walks found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default SafetyWalkListPage;
