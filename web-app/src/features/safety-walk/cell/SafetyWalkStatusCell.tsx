import { GridRenderCellParams } from '@mui/x-data-grid';
import { Lock, LockOpen } from '@mui/icons-material';
import { SafetyWalkRead, SafetyWalkStatus } from '../safetyWalkTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type SafetyWalkStatusCellParam = GridRenderCellParams<SafetyWalkRead, string>;

export default function SafetyWalkStatusCell(params: SafetyWalkStatusCellParam) {
  const { row, formattedValue: statusText } = params;
  const { status } = row;

  if (!statusText || !status) {
    return null;
  }

  const isLocked = status === SafetyWalkStatus.CANCELED || status === SafetyWalkStatus.RESOLVED;

  return (
    <Cell title={statusText}>
      {isLocked ? (
        <Lock fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      ) : (
        <LockOpen fontSize="small" sx={{ verticalAlign: 'text-top' }} />
      )}{' '}
      <CellText>{statusText}</CellText>
    </Cell>
  );
}
