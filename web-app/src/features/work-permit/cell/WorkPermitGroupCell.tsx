import { GridRenderCellParams } from '@mui/x-data-grid';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import { WorkPermitRead } from '../workPermitTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function WorkPermitGroupCell(params: GridRenderCellParams<WorkPermitRead>) {
  const { row } = params;
  const groupName = row.group?.name;

  if (!groupName) {
    return null;
  }

  return (
    <Cell title={groupName}>
      <PeopleAltOutlinedIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{groupName}</CellText>
    </Cell>
  );
}
