import { GridRenderCellParams } from '@mui/x-data-grid';
import { WorkPermitRead } from '../workPermitTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type WorkPermitNameCellParam = GridRenderCellParams<WorkPermitRead, string>;

export default function WorkPermitNameCell(params: WorkPermitNameCellParam) {
  const { formattedValue: name } = params;

  if (!name || name.length === 0) {
    return null;
  }

  return (
    <Cell title={name}>
      <CellText>{name}</CellText>
    </Cell>
  );
}
