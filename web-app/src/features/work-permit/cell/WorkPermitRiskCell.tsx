import { GridRenderCellParams } from '@mui/x-data-grid';
import WarningIcon from '@mui/icons-material/Warning';
import { RiskCategory, RiskCategoryDisplayMap, WorkPermitRead } from '../workPermitTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function WorkPermitRiskCell(params: GridRenderCellParams<WorkPermitRead>) {
  const { row } = params;
  const riskText = RiskCategoryDisplayMap[row.riskCategory];

  if (!row.riskCategory || !riskText) {
    return null;
  }

  return (
    <Cell title={riskText}>
      <WarningIcon
        fontSize="small"
        sx={{
          mr: 0.5,
          color: row.riskCategory === RiskCategory.HIGH ? 'error.main' : 'warning.light',
        }}
      />
      <CellText>{riskText}</CellText>
    </Cell>
  );
}
