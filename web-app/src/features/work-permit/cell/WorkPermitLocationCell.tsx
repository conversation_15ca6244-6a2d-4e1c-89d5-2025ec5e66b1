import { GridRenderCellParams } from '@mui/x-data-grid';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { WorkPermitRead } from '../workPermitTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function WorkPermitLocationCell(params: GridRenderCellParams<WorkPermitRead>) {
  const { row } = params;
  const locationName = row.location?.name;

  if (!locationName) {
    return null;
  }

  return (
    <Cell title={locationName}>
      <LocationOnIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{locationName}</CellText>
    </Cell>
  );
}
