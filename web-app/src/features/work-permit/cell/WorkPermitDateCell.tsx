import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import { Box } from '@mui/material';
import { WorkPermitRead } from '../workPermitTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';
import { getDatePlusTimeString } from '../../../utils';

type WorkPermitDateCellParams = GridRenderCellParams<WorkPermitRead>;

export default function WorkPermitDateCell(params: WorkPermitDateCellParams) {
  const { row } = params;

  const startTime = row.startTime ? getDatePlusTimeString(new Date(row.startTime)) : '';
  const endTime = row.endTime ? getDatePlusTimeString(new Date(row.endTime)) : '';

  if (!startTime || !endTime) {
    return null;
  }

  const title = `${startTime} - ${endTime}`;

  return (
    <Cell title={title}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
        <CellText>{startTime}</CellText>
        <ArrowRightAltIcon fontSize="small" sx={{ mx: 0.5, verticalAlign: 'text-top' }} />
        <CellText>{endTime}</CellText>
      </Box>
    </Cell>
  );
}
