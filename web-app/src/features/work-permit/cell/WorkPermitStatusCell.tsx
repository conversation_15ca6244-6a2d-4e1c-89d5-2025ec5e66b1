import { GridRenderCellParams } from '@mui/x-data-grid';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PeopleIcon from '@mui/icons-material/People';
import { WorkPermitRead, WorkPermitStatus, WorkPermitStatusDisplayMap } from '../workPermitTypes';
import { getTimeString } from '../../../utils';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type WorkPermitStatusCellParams = GridRenderCellParams<WorkPermitRead>;

export default function WorkPermitStatusCell(params: WorkPermitStatusCellParams) {
  const { row } = params;
  const statusText = WorkPermitStatusDisplayMap[row.status];

  if (!row.status || !statusText) {
    return null;
  }

  const LockStatusIcon = row.locked ? LockIcon : LockOpenIcon;

  return (
    <Cell title={statusText}>
      <LockStatusIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>
        {statusText}
        {row.status === WorkPermitStatus.ISSUED && (
          <>
            {' '}
            (
            <span style={{ display: 'inline', marginRight: '4px' }}>
              <PeopleIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> {row.actualWorkerCount}
            </span>
            <AccessTimeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
            {getTimeString(new Date(row.modifiedDate))})
          </>
        )}
      </CellText>
    </Cell>
  );
}
