import { api, buildInvalidatesTags, buildProvidesTags } from '../../api';
import { PaginatedResult } from '../../utils';
import {
  WorkPermitRead,
  WorkPermitCreate,
  WorkPermitUpdate,
  WorkPermitParams,
  WorkPermitChange,
  WorkPermitPDFParams,
} from './workPermitTypes';

export const workPermitApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getWorkPermits: builder.query<PaginatedResult<WorkPermitRead>, WorkPermitParams>({
      query: (params) => {
        const sort =
          params.sort && params.sort.length > 0 ? params.sort.map((s) => `${s.field}:${s.direction}`) : undefined;
        return {
          url: 'work-permits',
          params: {
            ...params,
            sort,
          },
        };
      },
      providesTags: (result) => buildProvidesTags({ rootTag: 'WorkPermit', response: result?.content }),
    }),
    getWorkPermit: builder.query<WorkPermitRead, number>({
      query: (id) => `work-permits/${id}`,
      providesTags: (result) => buildProvidesTags({ rootTag: 'WorkPermit', response: result?.id }),
    }),
    createWorkPermit: builder.mutation<WorkPermitRead, WorkPermitCreate>({
      query: (body) => ({
        url: 'work-permits',
        method: 'POST',
        body,
      }),
      invalidatesTags: () =>
        buildInvalidatesTags({ rootTag: 'WorkPermit', dependentTypes: ['Tra', 'Lototo', 'Task', 'Group'] }),
    }),
    updateWorkPermit: builder.mutation<WorkPermitRead, WorkPermitUpdate>({
      query: (body) => {
        const { id, ...workPermitUpdate } = body;
        return {
          url: `work-permits/${id}`,
          method: 'PUT',
          body: workPermitUpdate,
        };
      },
      invalidatesTags: (result) =>
        buildInvalidatesTags({ rootTag: 'WorkPermit', id: result?.id, dependentTypes: ['Tra', 'Lototo', 'Task'] }),
    }),
    downloadWorkPermitPdf: builder.mutation<void, WorkPermitPDFParams>({
      query: (paramsAndId) => {
        const { id, ...params } = paramsAndId;
        return {
          url: `work-permits/${id}/pdf`,
          params,
          method: 'GET',
          cache: 'default',
          responseHandler: async (response) => {
            const hiddenElement = document.createElement('a');
            hiddenElement.target = '_blank';
            hiddenElement.href = window.URL.createObjectURL(await response.blob());
            hiddenElement.click();
          },
        };
      },
    }),
    getWorkPermitHistory: builder.query<WorkPermitChange[], number>({
      query: (id) => `work-permits/${id}/history`,
      providesTags: (_result, _error, params) => buildProvidesTags({ rootTag: 'WorkPermit', response: params }),
    }),
    cancelWorkPermit: builder.mutation<void, number>({
      query: (id) => ({
        url: `work-permits/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, arg) =>
        buildInvalidatesTags({ rootTag: 'WorkPermit', id: arg, dependentTypes: ['Task'] }),
    }),
  }),
});

export const {
  useGetWorkPermitsQuery,
  useGetWorkPermitQuery,
  useCreateWorkPermitMutation,
  useUpdateWorkPermitMutation,
  useDownloadWorkPermitPdfMutation,
  useGetWorkPermitHistoryQuery,
  useCancelWorkPermitMutation,
} = workPermitApi;
