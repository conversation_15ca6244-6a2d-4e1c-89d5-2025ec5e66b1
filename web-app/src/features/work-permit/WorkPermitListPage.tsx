import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs, Tooltip, ToggleButton, ToggleButtonGroup } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import MapIcon from '@mui/icons-material/Map';
import ViewListIcon from '@mui/icons-material/ViewList';
import { useEffect, useState, useMemo } from 'react';
import { Link, useParams } from 'react-router-dom';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useGetWorkPermitsQuery } from './workPermitApi';
import {
  WorkPermitParams,
  WorkPermitStatus,
  WorkPermitColumn,
  WorkPermitColumnDisplayMap,
  WorkPermitColumnDefaults,
  WorkPermitSort,
  WorkPermitFieldSortMap,
  WorkPermitRead,
} from './workPermitTypes';
import { useGetCurrentUserQuery } from '../user/userApi';
import WorkPermitChipFilter from './WorkPermitChipFilter';
import WorkPermitFilterBar from './WorkPermitFilterBar';
import Guard from '../guard/Guard';
import { GuardResult } from '../guard/guardHooks';
import { UserRole } from '../user/userTypes';
import { PermissionType } from '../guard/guardTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import { useAppDispatch, useAppSelector } from '../../store';
import { setWorkPermitViewState } from './workPermitSlice';
import WorkPermitMap from './WorkPermitMap';
import { LocationFilterMode } from '../location/locationTypes';
import NoRowsOverlay from '../../components/NoRowsOverlay';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import SidCell from '../../components/SidCell';
import WorkPermitDateCell from './cell/WorkPermitDateCell';
import WorkPermitGroupCell from './cell/WorkPermitGroupCell';
import WorkPermitLocationCell from './cell/WorkPermitLocationCell';
import WorkPermitNameCell from './cell/WorkPermitNameCell';
import WorkPermitRiskCell from './cell/WorkPermitRiskCell';
import WorkPermitStatusCell from './cell/WorkPermitStatusCell';
import usePaging from '../../components/hooks/usePaging';

const getWorkPermitUrl = (workPermitId: number) => `./${workPermitId}`;

const getSortFromGridModel = (model: GridSortModel): WorkPermitSort[] =>
  model
    .map((item) => {
      const mappedField = WorkPermitFieldSortMap[item.field as keyof WorkPermitRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is WorkPermitSort => sort !== null);

const getGridModelFromSort = (sort: WorkPermitSort[]): GridSortModel =>
  sort
    .map((item) => {
      // Find the grid field whose value in WorkPermitFieldSortMap equals the backend sort field
      const gridFieldEntry = Object.entries(WorkPermitFieldSortMap).find(([, value]) => value === item.field);
      if (!gridFieldEntry) {
        return null;
      }
      const [gridField] = gridFieldEntry;
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

const columnDefaults: Record<WorkPermitColumn, GridColDef<WorkPermitRead>> = {
  [WorkPermitColumn.SID]: {
    field: WorkPermitColumn.SID,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(SidCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.NAME]: {
    field: WorkPermitColumn.NAME,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.NAME],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitNameCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.GROUP]: {
    field: WorkPermitColumn.GROUP,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitGroupCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.LOCATION]: {
    field: WorkPermitColumn.LOCATION,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.LOCATION],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitLocationCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.RISK_CATEGORY]: {
    field: WorkPermitColumn.RISK_CATEGORY,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.RISK_CATEGORY],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitRiskCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.STATUS]: {
    field: WorkPermitColumn.STATUS,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitStatusCell(params), getWorkPermitUrl(params.row.id)),
  },
  [WorkPermitColumn.DATE]: {
    field: WorkPermitColumn.DATE,
    headerName: WorkPermitColumnDisplayMap[WorkPermitColumn.DATE],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<WorkPermitRead>) =>
      DataGridCellLinkWrapper(WorkPermitDateCell(params), getWorkPermitUrl(params.row.id)),
  },
};

function WorkPermitListPage() {
  const { groupId } = useParams();
  const { data: me } = useGetCurrentUserQuery();
  const workPermitViewState = useAppSelector((state) => state.workPermit.workPermitViewState);
  const dispatch = useAppDispatch();
  const { page, setPage, pageSize, setPageSize } = usePaging();
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(workPermitViewState?.sort || []));

  const columns = useMemo(() => {
    const cols = workPermitViewState.columns ? workPermitViewState.columns : WorkPermitColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [workPermitViewState.columns]);

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newViewMode: 'table' | 'map' | null) => {
    if (newViewMode !== null) {
      // Update Redux state directly
      dispatch(setWorkPermitViewState({ ...workPermitViewState, viewMode: newViewMode }));
    }
  };

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || workPermitViewState.listView;
    if (usedView === 'all') {
      return `(ancestorGroupId=${groupId}|locationOwnerAncestor=${groupId})`;
    }
    return `
      statusNot=${WorkPermitStatus.CLOSED}&statusNot=${WorkPermitStatus.CANCELED}
      &
      (ancestorGroupId=${groupId}|locationOwnerAncestor=${groupId})
    `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [queryParams, setQueryParams] = useState<WorkPermitParams>({
    pageSize,
    pageNumber: page,
    filter: getFilter(),
    candidateGroups: workPermitViewState?.candidateGroups,
    groupId: workPermitViewState?.group?.id,
    status: workPermitViewState?.status,
    riskCategory: workPermitViewState?.riskCategory,
    workMethodIds:
      workPermitViewState?.workMethods && workPermitViewState?.workMethods.length > 0
        ? workPermitViewState.workMethods.map((w) => w.id)
        : undefined,
    createdBy: workPermitViewState?.createdBy,
    search: workPermitViewState?.search,
    sort: workPermitViewState?.sort,
    ancestorLocationId:
      workPermitViewState?.locationFilterMode === LocationFilterMode.UNDER
        ? workPermitViewState?.location?.id
        : undefined,
    locationId:
      workPermitViewState?.locationFilterMode === LocationFilterMode.EQUALS
        ? workPermitViewState?.location?.id
        : undefined,
  });
  const { data, isLoading, error } = useGetWorkPermitsQuery(queryParams);

  const canRequestPermit = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.WORKPERMIT_CREATE);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageNumber: page,
      pageSize,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (workPermitViewState) {
      setQueryParams((prev) => ({
        ...prev,
        candidateGroups: workPermitViewState?.candidateGroups,
        groupId: workPermitViewState?.group?.id,
        status: workPermitViewState?.status,
        riskCategory: workPermitViewState?.riskCategory,
        workMethodIds:
          workPermitViewState?.workMethods && workPermitViewState.workMethods.length > 0
            ? workPermitViewState.workMethods.map((w) => w.id)
            : undefined,
        createdBy: workPermitViewState?.createdBy,
        search: workPermitViewState?.search,
        sort: workPermitViewState?.sort,
        ancestorLocationId:
          workPermitViewState?.locationFilterMode === LocationFilterMode.UNDER
            ? workPermitViewState?.location?.id
            : undefined,
        locationId:
          workPermitViewState?.locationFilterMode === LocationFilterMode.EQUALS
            ? workPermitViewState?.location?.id
            : undefined,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workPermitViewState]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setWorkPermitViewState({
        ...workPermitViewState,
        listView: view,
      })
    );
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(view),
    }));
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setWorkPermitViewState({ ...workPermitViewState, sort: getSortFromGridModel(newModel) }));
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Work permits" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={workPermitViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Box display="flex" gap={2}>
            <ToggleButtonGroup
              value={workPermitViewState.viewMode || 'table'}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
              size="small"
            >
              <ToggleButton value="table" aria-label="table view">
                <Tooltip title="Table View">
                  <ViewListIcon />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="map" aria-label="map view">
                <Tooltip title="Map View">
                  <MapIcon />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <Guard hasAccess={canRequestPermit}>
              <ResponsiveButton component={Link} to="request" variant="contained" size="large" endIcon={<AddIcon />}>
                Request permit
              </ResponsiveButton>
            </Guard>
          </Box>
        </Box>
        <WorkPermitChipFilter me={me?.id} resetPageNumber={resetPageNumber} />
        <WorkPermitFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel sx={{ px: 0, pt: 1, pb: 0 }} value="0">
          {(workPermitViewState.viewMode || 'table') === 'table' ? (
            <Paper elevation={4}>
              <Box
                sx={{
                  height: 'calc(100vh - 269px)',
                  overflow: 'hidden',
                  '@media (max-height: 600px)': {
                    height: '100%',
                  },
                }}
              >
                <DataGrid
                  rows={data?.content || []}
                  columns={columns}
                  rowCount={data?.total || 0}
                  loading={isLoading}
                  disableColumnMenu
                  pagination
                  paginationMode="server"
                  paginationModel={{ page, pageSize }}
                  onPaginationModelChange={handlePaginationChange}
                  sortingMode="server"
                  sortModel={sortModel}
                  onSortModelChange={handleSortModelChange}
                  disableRowSelectionOnClick
                  slots={{
                    noRowsOverlay: NoRowsOverlay,
                  }}
                  onColumnWidthChange={(params) => {
                    const newViewState = { ...workPermitViewState };
                    if (newViewState.columns) {
                      // Clone the columns array to avoid direct mutation.
                      const updatedColumns = [...newViewState.columns];
                      // Find the column to update.
                      const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                      if (columnToUpdate) {
                        // Get the index of the column and update immutably.
                        const index = updatedColumns.indexOf(columnToUpdate);
                        updatedColumns[index] = { ...columnToUpdate, width: params.width };
                      }
                      newViewState.columns = updatedColumns;
                    }
                    dispatch(setWorkPermitViewState(newViewState));
                  }}
                  slotProps={{
                    loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                    noRowsOverlay: { title: 'No work permits found' },
                  }}
                />
              </Box>
            </Paper>
          ) : (
            <WorkPermitMap groupId={groupId || ''} params={queryParams} listView={workPermitViewState.listView} />
          )}
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default WorkPermitListPage;
