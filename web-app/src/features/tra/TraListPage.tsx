import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link as RouterLink, useParams } from 'react-router-dom';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import { useGetTrasQuery } from './traApi';
import {
  TraColumn,
  TraColumnDefaults,
  TraColumnDisplayMap,
  TraFieldSortMap,
  TraParams,
  TraRead,
  TraSort,
} from './traTypes';
import TraFilterBar from './TraFilterBar';
import TraChipFilter from './TraChipFilter';
import { useGetCurrentUserQuery } from '../user/userApi';
import { GuardResult } from '../guard/guardHooks';
import { UserRole } from '../user/userTypes';
import { PermissionType } from '../guard/guardTypes';
import Guard from '../guard/Guard';
import { WorkPermitStatus } from '../work-permit/workPermitTypes';
import { setTraViewState } from './traSlice';
import { useAppDispatch, useAppSelector } from '../../store';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import TraNameCell from './cell/TraNameCell';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import TraWorkPermitCell from './cell/TraWorkPermitCell';
import { GroupDisplay } from '../group/groupTypes';

const getTraUrl = (traId: number) => `./${traId}`;

const getSortFromGridModel = (model: GridSortModel): TraSort[] =>
  model
    .map((item) => {
      const mappedField = TraFieldSortMap[item.field as keyof TraRead];
      if (!mappedField) {
        return null;
      }

      return {
        field: mappedField,
        direction: item.sort,
      };
    })
    .filter((sort): sort is TraSort => sort !== null);

const getGridModelFromSort = (sort: TraSort[]): GridSortModel =>
  sort
    .map((item) => {
      // Find the grid field whose value in TraFieldSortMap equals the backend sort field
      const gridField = Object.entries(TraFieldSortMap).find(([, value]) => value === item.field)?.[0];
      if (!gridField) {
        return null;
      }
      return { field: gridField, sort: item.direction };
    })
    .filter((s) => s !== null) as GridSortModel;

const columnDefaults: Record<TraColumn, GridColDef<TraRead>> = {
  [TraColumn.SID]: {
    field: TraColumn.SID,
    headerName: TraColumnDisplayMap[TraColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<TraRead>) =>
      DataGridCellLinkWrapper(SidCell(params), getTraUrl(params.row.id)),
  },
  [TraColumn.NAME]: {
    field: TraColumn.NAME,
    headerName: TraColumnDisplayMap[TraColumn.NAME],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<TraRead>) =>
      DataGridCellLinkWrapper(TraNameCell(params), getTraUrl(params.row.id)),
  },
  [TraColumn.GROUP]: {
    field: TraColumn.GROUP,
    headerName: TraColumnDisplayMap[TraColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<TraRead>) =>
      DataGridCellLinkWrapper(GroupCell(params), getTraUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [TraColumn.WORK_PERMIT]: {
    field: TraColumn.WORK_PERMIT,
    headerName: TraColumnDisplayMap[TraColumn.WORK_PERMIT],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<TraRead>) =>
      DataGridCellLinkWrapper(TraWorkPermitCell(params), getTraUrl(params.row.id)),
  },
};

function TraListPage() {
  const { data: me } = useGetCurrentUserQuery();
  const { groupId } = useParams();
  const traViewState = useAppSelector((state) => state.tra.traViewState);
  const dispatch = useAppDispatch();
  const { page, setPage, pageSize, setPageSize } = usePaging();
  const [sortModel, setSortModel] = useState<GridSortModel>(
    traViewState?.sort ? getGridModelFromSort(traViewState.sort) : []
  );

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || traViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `(workPermitStatusNot=${WorkPermitStatus.CLOSED}&workPermitStatusNot=${WorkPermitStatus.CANCELED})|hasWorkPermit=false`;
  };

  const columns = useMemo(() => {
    const cols = traViewState.columns ? traViewState.columns : TraColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [traViewState.columns]);

  const [queryParams, setQueryParams] = useState<TraParams>({
    ancestorGroupId: Number(groupId),
    createdBy: traViewState.createdBy,
    groupId: traViewState.group?.id,
    search: traViewState.search,
    filter: getFilter(),
    pageSize,
    pageNumber: page,
    sort: traViewState?.sort,
  });
  const { data, isLoading, error } = useGetTrasQuery(queryParams);
  const canRequestTra = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.WORKPERMIT_CREATE);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageNumber: page,
      pageSize,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (traViewState) {
      setQueryParams((prev) => ({
        ...prev,
        groupId: traViewState?.group?.id,
        createdBy: traViewState?.createdBy,
        search: traViewState?.search,
        sort: traViewState?.sort,
      }));

      if (traViewState.sort) {
        setSortModel(getGridModelFromSort(traViewState.sort));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [traViewState]);

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);
    dispatch(setTraViewState({ ...traViewState, sort: getSortFromGridModel(newModel) }));
  };

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setTraViewState({
        ...traViewState,
        listView: view,
      })
    );
    setQueryParams((prev) => ({
      ...prev,
      pageNumber: 0,
      filter: getFilter(view),
    }));
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const resetPageNumber = () => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="TRA's" />
      <TabContext value="0">
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Tabs value={traViewState.listView}>
              <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
              <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
            </Tabs>
            <Guard hasAccess={canRequestTra}>
              <ResponsiveButton component={RouterLink} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Add TRA
              </ResponsiveButton>
            </Guard>
          </Box>
          <TraChipFilter me={me?.id} resetPageNumber={resetPageNumber} />
          <TraFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        </Box>
        <TabPanel
          value="0"
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...traViewState };
                  if (newViewState.columns) {
                    const updatedColumns = [...newViewState.columns];
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setTraViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No TRAs found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default TraListPage;
