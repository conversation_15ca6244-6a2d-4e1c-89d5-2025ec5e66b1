import { useState, useEffect } from 'react';
import { <PERSON>, Stack, IconButton, Popover, Tooltip, Typography, Checkbox, FormControlLabel } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useAppDispatch, useAppSelector } from '../../store';
import { setTraViewState } from './traSlice';
import { TraColumnDefaults, TraColumn, TraColumnDisplayMap, TraColumnSetting } from './traTypes';

const mergeColumns = (
  persistedColumns: TraColumnSetting[] = [],
  defaultColumns: TraColumnSetting[] = []
): TraColumnSetting[] => {
  // Keep only the persisted columns that still exist in the defaults.
  const validPersisted = persistedColumns.filter((pc) => defaultColumns.some((dc) => dc.column === pc.column));

  // Create a set of the column keys in the valid persisted list.
  const persistedSet = new Set(validPersisted.map((pc) => pc.column));

  // Find any default columns that the customer hasn't chosen yet.
  const newDefaults = defaultColumns.filter((dc) => !persistedSet.has(dc.column));

  // Return the persisted order first, then add new columns at the end.
  return [...validPersisted, ...newDefaults];
};

interface TraChipFilterProps {
  me?: number;
  resetPageNumber: () => void;
}

function TraChipFilter({ me, resetPageNumber }: TraChipFilterProps) {
  const traViewState = useAppSelector((state) => state.tra.traViewState);
  const dispatch = useAppDispatch();

  // Use merged columns: combine persisted columns with defaults
  const initialColumnsOrder: TraColumnSetting[] = mergeColumns(traViewState?.columns, TraColumnDefaults);
  const [columnsOrder, setColumnsOrder] = useState<TraColumnSetting[]>(initialColumnsOrder);

  useEffect(() => {
    if (traViewState?.columns) {
      const mergedColumns = mergeColumns(traViewState.columns, TraColumnDefaults);
      setColumnsOrder(mergedColumns);
      // Check if the merged result is different from the persisted state.
      if (JSON.stringify(mergedColumns) !== JSON.stringify(traViewState.columns)) {
        dispatch(setTraViewState({ ...traViewState, columns: mergedColumns }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [traViewState?.columns]);

  // Popover state for the "View options" button.
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const handleFineTuneClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handlePopoverClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);
  const id = open ? 'fine-tune-popover' : undefined;

  // Local state to track drag index.
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      dispatch(setTraViewState({ ...traViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: TraColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setTraViewState({ ...traViewState, columns: newOrder }));
  };

  return (
    <Stack
      direction="row"
      flexWrap={{ xs: 'nowrap', sm: 'wrap' }}
      overflow={{ xs: 'scroll', sm: 'unset' }}
      alignItems="center"
    >
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={traViewState.createdBy ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...traViewState };
          if (traViewState.createdBy) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setTraViewState(newState));
          resetPageNumber();
        }}
      />

      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleFineTuneClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              px: 2,
              py: 1,
              maxWidth: 300,
            },
          },
        }}
      >
        <Stack>
          <Typography variant="subtitle1">View options</Typography>
          <Typography variant="subtitle1" sx={{ mt: 1 }}>
            Columns
          </Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={TraColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}
export default TraChipFilter;
