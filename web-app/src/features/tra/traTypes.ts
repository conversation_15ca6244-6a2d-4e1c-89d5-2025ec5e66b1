import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { UserDisplay } from '../user/userTypes';
import { WorkPermitDisplay, WorkPermitStatus } from '../work-permit/workPermitTypes';

export interface TraParams {
  groupId?: number;
  ancestorGroupId?: number;
  createdBy?: number;
  workPermitStatusNot?: WorkPermitStatus;
  hasWorkPermit?: boolean;
  dateLte?: number;
  dateGte?: number;
  filter?: string;
  search?: string;
  pageNumber?: number;
  pageSize?: number;
  sort?: TraSort[];
}

export interface TraViewState {
  listView: 'mine' | 'all';
  createdBy?: number;
  group?: GroupListRead;
  search?: string;
  sort?: TraSort[];
  columns?: TraColumnSetting[];
}

export interface TraListRead {
  id: number;
  sid: number;
  name: string;
  group: GroupDisplay;
  workPermit?: WorkPermitDisplay;
}

export interface TraRead {
  id: number;
  sid: number;
  name: string;
  group: GroupDisplay;
  participants: string;
  workPermit?: WorkPermitDisplay;
  traRows: TraRowRead[];
  createdBy: UserDisplay;
}

export interface TraRowRead {
  id: number;
  headStep: string;
  subStep: string;
  risk: string;
  measure: string;
  order: number;
}

export interface TraCreate {
  name: string;
  group: number;
  traRows: TraRowUpdate[];
  participants: string;
}

export interface TraUpdate {
  id: number;
  name: string;
  traRows: TraRowUpdate[];
  participants: string;
}

export interface TraRowUpdate {
  id?: number;
  headStep: string;
  subStep: string;
  risk: string;
  measure: string;
  order: number;
}

export interface TraFormInput {
  name: string;
  group: GroupDisplay | null;
  traRows: TraRowUpdate[];
  participants: string;
}

export interface TraChange {
  by: UserDisplay;
  at: number;
  type: TraChangeType;
  oldEntity: TraRead;
  newEntity: TraRead;
}

export enum TraChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const TraChangeTypeDisplayMap: Record<TraChangeType, string> = {
  INSERT: 'TRA created',
  UPDATE: 'TRA updated',
  DELETE: 'TRA deleted',
};

export interface TraState {
  traCopy?: Partial<TraCreate>;
  traViewState: TraViewState;
}

export enum TraColumn {
  SID = 'sid',
  NAME = 'name',
  GROUP = 'group',
  WORK_PERMIT = 'workPermit',
}

export const TraColumnDisplayMap: Record<TraColumn, string> = {
  [TraColumn.SID]: 'ID',
  [TraColumn.NAME]: 'Title',
  [TraColumn.GROUP]: 'Created by',
  [TraColumn.WORK_PERMIT]: 'Work permit',
};

export interface TraColumnSetting {
  column: TraColumn;
  hidden: boolean;
  width: number;
}

export const TraColumnDefaults: TraColumnSetting[] = [
  { column: TraColumn.SID, width: 75, hidden: false },
  { column: TraColumn.NAME, width: 1115, hidden: false },
  { column: TraColumn.GROUP, width: 200, hidden: false },
  { column: TraColumn.WORK_PERMIT, width: 200, hidden: false },
];

export enum TraSortField {
  SID = 'sid',
  NAME = 'name',
  CREATION_DATE = 'creation_date',
  MODIFIED_DATE = 'modified_date',
}

export const TraFieldSortMap: Partial<Record<keyof TraRead, TraSortField>> = {
  sid: TraSortField.SID,
  name: TraSortField.NAME,
};

export interface TraSort {
  field: TraSortField;
  direction: 'asc' | 'desc';
}

export enum TraGroupBy {
  GROUP = 'TRAS_GROUP',
  CREATION_DATE_WEEK = 'TRAS_CREATION_DATE_WEEK',
}

export const TraGroupByDisplayMap: Record<TraGroupBy, string> = {
  [TraGroupBy.GROUP]: 'Group',
  [TraGroupBy.CREATION_DATE_WEEK]: 'TRA week',
};
