import { GridRenderCellParams } from '@mui/x-data-grid';
import { TraRead } from '../traTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type TraNameCellParam = GridRenderCellParams<TraRead, string>;

export default function TraNameCell(params: TraNameCellParam) {
  const { formattedValue: name } = params;

  if (!name || name.length === 0) {
    return null;
  }

  return (
    <Cell title={name}>
      <CellText>{name}</CellText>
    </Cell>
  );
}
