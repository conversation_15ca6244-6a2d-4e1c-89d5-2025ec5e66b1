import { Link } from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import { Link as RouterLink } from 'react-router-dom';
import { TraRead } from '../traTypes';
import { ROUTES } from '../../../constants';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

function TraWorkPermitCell({ row }: GridRenderCellParams<TraRead>) {
  const { workPermit } = row;

  if (!workPermit || !workPermit.id || !workPermit.sid) {
    return null;
  }

  return (
    <Cell>
      <CellText sx={{ display: 'flex', alignItems: 'center' }}>
        <Link
          underline="hover"
          component={RouterLink}
          to={`./..${ROUTES.WORK_PERMITS}/${workPermit.id}`}
          sx={{ display: 'inline-flex', alignItems: 'center' }}
        >
          #{workPermit.sid}
        </Link>
      </CellText>
      <CellText>{workPermit.name}</CellText>
    </Cell>
  );
}

export default TraWorkPermitCell;
