import { api, buildInvalidatesTags, buildProvidesTags } from '../../api';
import { PaginatedResult } from '../../utils';
import { TraRead, TraCreate, TraUpdate, TraParams, TraChange } from './traTypes';

export const traApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getTras: builder.query<PaginatedResult<TraRead>, TraParams>({
      query: (params) => {
        const sort =
          params.sort && params.sort.length > 0 ? params.sort.map((s) => `${s.field}:${s.direction}`) : undefined;
        return {
          url: 'tras',
          params: {
            ...params,
            sort,
          },
        };
      },
      providesTags: (result) => buildProvidesTags({ rootTag: 'Tra', response: result?.content }),
    }),
    getTra: builder.query<TraRead, number>({
      query: (id) => `tras/${id}`,
      providesTags: (result) => buildProvidesTags({ rootTag: 'Tra', response: result }),
    }),
    createTra: builder.mutation<TraRead, TraCreate>({
      query: (body) => ({
        url: 'tras',
        method: 'POST',
        body,
      }),
      invalidatesTags: () => buildInvalidatesTags({ rootTag: 'Tra', dependentTypes: ['Task'] }),
    }),
    updateTra: builder.mutation<TraRead, TraUpdate>({
      query: (body) => {
        const { id, ...traUpdate } = body;
        return {
          url: `tras/${id}`,
          method: 'PUT',
          body: traUpdate,
        };
      },
      invalidatesTags: (result) => buildInvalidatesTags({ rootTag: 'Tra', id: result?.id, dependentTypes: ['Task'] }),
    }),
    downloadTraPdf: builder.mutation<void, number>({
      query: (id) => ({
        url: `tras/${id}/pdf`,
        method: 'GET',
        cache: 'default',
        responseHandler: async (response) => {
          const hiddenElement = document.createElement('a');
          hiddenElement.target = '_blank';
          hiddenElement.href = window.URL.createObjectURL(await response.blob());
          hiddenElement.click();
        },
      }),
    }),
    getTraHistory: builder.query<TraChange[], number>({
      query: (id) => `tras/${id}/history`,
      providesTags: (result, error, params) => buildProvidesTags({ rootTag: 'Tra', response: params }),
    }),
    deleteTra: builder.mutation<void, number>({
      query: (id) => ({
        url: `tras/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, arg) =>
        buildInvalidatesTags({ rootTag: 'Tra', id: arg, dependentTypes: ['Task'] }),
    }),
  }),
});

export const {
  useGetTrasQuery,
  useGetTraQuery,
  useCreateTraMutation,
  useUpdateTraMutation,
  useDownloadTraPdfMutation,
  useGetTraHistoryQuery,
  useDeleteTraMutation,
} = traApi;
