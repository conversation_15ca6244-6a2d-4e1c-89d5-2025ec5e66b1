package com.vinkey.restapi.permittowork.workpermit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.vinkey.restapi.BaseControllerTest;
import com.vinkey.restapi.ResponseBodyMatcher;
import com.vinkey.restapi.WithMockJWTDetails;
import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantMother;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitChangeBuilder;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitCreateBuilder;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitMother;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitUpdateBuilder;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitChange;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitCreate;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitRead;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitUpdate;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettingsService;
import com.vinkey.restapi.permittowork.workpermit.print.builder.PrintSettingsBuilder;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

@WebMvcTest(WorkPermitController.class)
public class WorkPermitControllerTest extends BaseControllerTest {
  @MockBean private WorkPermitService workPermitService;

  @MockBean private WorkPermitHistoryService workPermitHistoryService;

  @MockBean private PrintSettingsService printSettingsService;

  @MockBean private FileService fileService;

  private WorkPermitMapper workPermitMapper = WorkPermitMapper.INSTANCE;

  @Test
  @WithMockJWTDetails
  public void createWorkPermit_withCreate_returnIsCreatedAndPermit() throws Exception {
    WorkPermitCreate workPermitCreate = WorkPermitCreateBuilder.aValidCreate().build();
    WorkPermit workPermit = workPermitMapper.workPermitCreateToWorkPermit(workPermitCreate);
    workPermit.setTenant(TenantMother.aSavedTenant().build());
    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId("rndId");
    processInstanceCustom.setProcessInstanceId("strId");
    workPermit.setProcessInstance(processInstanceCustom);
    WorkPermitRead workPermitRead = workPermitMapper.workPermitToWorkPermitRead(workPermit);

    String json = mapper.writeValueAsString(workPermitCreate);

    given(workPermitService.createWorkPermit(any(WorkPermit.class))).willReturn(workPermit.getId());
    given(workPermitService.getWorkPermit(workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(workPermit);
    ResultActions result =
        mockMvc.perform(
            post("/v1/work-permits").contentType(MediaType.APPLICATION_JSON).content(json));

    result
        .andExpect(status().isCreated())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(workPermitRead));
    // JSONAssert.assertEquals(jsonResponse, expectedJson, false);
  }

  @Test
  @WithMockJWTDetails
  public void createWorkPermit_withNull_returnsBadRequest() throws Exception {
    WorkPermitCreate workPermitCreate = WorkPermitCreateBuilder.anEmptyCreate().build();

    String json = mapper.writeValueAsString(workPermitCreate);

    ResultActions result =
        mockMvc.perform(
            post("/v1/work-permits").contentType(MediaType.APPLICATION_JSON).content(json));

    result.andExpect(status().isInternalServerError());
  }

  @Test
  @WithMockJWTDetails
  public void getWorkPermits_withAllArgsNoFilter_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();

    Long tenantId = jwtDetails.getTenantId();
    WorkPermit workPermit1 = WorkPermitMother.aSavedWorkPermit().build();
    WorkPermit workPermit2 = WorkPermitMother.aSavedWorkPermit().but().withId(88L).build();
    Long groupId = 1L;
    WorkPermitStatus status = WorkPermitStatus.APPROVED;
    WorkPermitStatus statusNot = WorkPermitStatus.ISSUED;
    RiskCategory riskCategory = RiskCategory.HIGH;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(3L);
    Long createdBy = 3L;
    Long lototoPlan = 3L;
    String search = "sampleSearch";
    Long ancestorGroupId = 4L;
    Long creationDateGte = 1638220800000L; // Assuming a timestamp value for testing
    Long creationDateLte = 1640908800000L; // Assuming a timestamp value for testing
    List<String> candidateGroups = Arrays.asList("group1", "group2");
    List<Long> candidateUsers = Arrays.asList(5L, 6L);
    Long locationOwner = 7L;
    Long locationOwnerAncestor = 8L;
    Long excludeUser = 9L;
    Long pageSize = 10L;
    Long pageNumber = 1L;
    String filter =
        "tenantId=1|(workMethods=1,2&workMethods=3,4,5)&groupId=4&locationOwner=3&locationOwnerAncestor=6"
            + "&status=ISSUED&createdBy=8&candidateGroups=(Shell%20Group,Esso%20Group)&excludeUser=9"
            + "&candidateUsers=(1,2,3,4,5)&ancestorGroupId=55&creationDateGte=123123&creationDateGte=234234"
            + "statusNot=APPROVED&riskCategory=HIGH&search=Lifting%20permit";

    List.of(1L, 2L, 3L, 4L, 5L);

    List<WorkPermit> list = Arrays.asList(workPermit1, workPermit2);
    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(WorkPermit_.ID).descending());
    Page<WorkPermit> page = new PageImpl<>(list, pageable, list.size());

    given(
            workPermitService.getWorkPermits(
                tenantId,
                groupId,
                status,
                statusNot,
                riskCategory,
                workMethodIds,
                createdBy,
                lototoPlan,
                creationDateGte,
                creationDateLte,
                search,
                filter,
                null,
                pageSize,
                pageNumber,
                candidateGroups,
                candidateUsers,
                ancestorGroupId,
                locationOwner,
                locationOwnerAncestor,
                (Long) null, // locationId
                (Long) null, // ancestorLocationId
                excludeUser))
        .willReturn(page);

    ResultActions result =
        mockMvc.perform(
            get("/v1/work-permits")
                .queryParam("status", status.name())
                .param("groupId", groupId.toString())
                .queryParam("statusNot", statusNot.name())
                .queryParam("riskCategory", riskCategory.name())
                .queryParam(
                    "workMethodIds",
                    workMethodIds.stream().map(Object::toString).collect(Collectors.joining(",")))
                .queryParam("createdBy", createdBy.toString())
                .queryParam("lototoPlan", lototoPlan.toString())
                .queryParam("search", search)
                .queryParam("ancestorGroupId", ancestorGroupId.toString())
                .queryParam("creationDateGte", creationDateGte.toString())
                .queryParam("creationDateLte", creationDateLte.toString())
                .queryParam(
                    "candidateGroups",
                    candidateGroups.stream().map(Object::toString).collect(Collectors.joining(",")))
                .queryParam(
                    "candidateUsers",
                    candidateUsers.stream().map(Object::toString).collect(Collectors.joining(",")))
                .queryParam("locationOwner", locationOwner.toString())
                .queryParam("locationOwnerAncestor", locationOwnerAncestor.toString())
                .queryParam("excludeUser", excludeUser.toString())
                .queryParam("pageSize", pageSize.toString())
                .queryParam("pageNumber", pageNumber.toString())
                .param("filter", filter)
                .contentType(MediaType.APPLICATION_JSON));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(workPermitMapper.workPermitsToWorkPermitReads(page)));
  }

  @Test
  @WithMockJWTDetails
  public void updateWorkPermit_withIdandUpdate_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    WorkPermitUpdate workPermitUpdate = WorkPermitUpdateBuilder.aValidUpdate().build();
    workPermitMapper.workPermitUpdateToWorkPermit(workPermit, workPermitUpdate);

    String json = mapper.writeValueAsString(workPermitUpdate);

    given(workPermitService.getWorkPermit(workPermit.getId(), jwtDetails.getTenantId()))
        .willReturn(workPermit);

    ResultActions result =
        mockMvc.perform(
            put("/v1/work-permits/{id}", workPermit.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(json));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(workPermitMapper.workPermitToWorkPermitRead(workPermit)));
  }

  @Test
  @WithMockJWTDetails
  public void updateWorkPermit_withUpdateButWrongId_returnsInternalServerError() throws Exception {
    WorkPermitUpdate workPermitUpdate = WorkPermitUpdateBuilder.aValidUpdate().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    String json = mapper.writeValueAsString(workPermitUpdate);

    given(workPermitService.getWorkPermit(1L, jwtDetails.getTenantId()))
        .willThrow(RuntimeException.class);

    ResultActions result =
        mockMvc.perform(
            put("/v1/work-permits/{id}", 1L).contentType(MediaType.APPLICATION_JSON).content(json));

    result.andExpect(status().isInternalServerError());
  }

  @Test
  @WithMockJWTDetails
  public void cancelWorkPermit_withId_returnsNoContent() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();

    given(workPermitService.getWorkPermit(workPermit.getId(), jwtDetails.getTenantId()))
        .willReturn(workPermit);

    ResultActions result = mockMvc.perform(delete("/v1/work-permits/{id}", workPermit.getId()));

    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void cancelWorkPermit_withWrongId_returnsInternalServerError() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();

    given(workPermitService.getWorkPermit(3L, jwtDetails.getTenantId()))
        .willThrow(RuntimeException.class);

    ResultActions result = mockMvc.perform(delete("/v1/work-permits/{id}", 3L));

    result.andExpect(status().isInternalServerError());
  }

  @Test
  @WithMockJWTDetails
  public void generateWorkPermit_withId_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    PrintSettings printSettings = PrintSettingsBuilder.aValidPrintSettings().build();

    given(workPermitService.getWorkPermit(workPermit.getId(), jwtDetails.getTenantId()))
        .willReturn(workPermit);
    given(printSettingsService.getPrintSettingsById(jwtDetails.getTenantId()))
        .willReturn(printSettings);

    ResultActions result =
        mockMvc.perform(
            get("/v1/work-permits/{id}/pdf", workPermit.getId())
                .param("timeZone", "UTC")
                .param("copy", "false")
                .contentType(MediaType.APPLICATION_PDF_VALUE));

    verify(workPermitService)
        .generateWorkPermit(
            eq(workPermit), eq(printSettings), eq("UTC"), eq(false), any(OutputStream.class));
    result.andExpect(status().isOk());
  }

  @Test
  @WithMockJWTDetails
  public void getWorkPermitHistory_withId_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    WorkPermitChange workPermitChange = WorkPermitChangeBuilder.aValidChange().build();
    List<WorkPermitChange> list = List.of(workPermitChange);

    given(workPermitHistoryService.getHistory(1L, jwtDetails.getTenantId())).willReturn(list);

    ResultActions result = mockMvc.perform(get("/v1/work-permits/{id}/history", 1L));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(list));
  }
}
