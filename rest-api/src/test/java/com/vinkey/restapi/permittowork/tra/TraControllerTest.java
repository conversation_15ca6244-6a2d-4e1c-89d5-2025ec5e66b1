package com.vinkey.restapi.permittowork.tra;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vinkey.restapi.ResponseBodyMatcher;
import com.vinkey.restapi.WithMockJWTDetails;
import com.vinkey.restapi.identityandaccess.auth.access.AccessTokenService;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.auth.jwt.JwtIssuerService;
import com.vinkey.restapi.identityandaccess.identity.IdentityDetailService;
import com.vinkey.restapi.identityandaccess.user.builder.UserDisplayBuilder;
import com.vinkey.restapi.permittowork.tra.builder.TraCreateBuilder;
import com.vinkey.restapi.permittowork.tra.builder.TraMother;
import com.vinkey.restapi.permittowork.tra.builder.TraReadBuilder;
import com.vinkey.restapi.permittowork.tra.builder.TraUpdateBuilder;
import com.vinkey.restapi.permittowork.tra.dto.TraChange;
import com.vinkey.restapi.permittowork.tra.dto.TraCreate;
import com.vinkey.restapi.permittowork.tra.dto.TraRead;
import com.vinkey.restapi.permittowork.tra.dto.TraUpdate;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitStatus;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettingsService;
import com.vinkey.restapi.permittowork.workpermit.print.builder.PrintSettingsBuilder;
import java.io.OutputStream;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

@WebMvcTest(TraController.class)
public class TraControllerTest {

  private static ObjectMapper mapper = new ObjectMapper();

  @Autowired private MockMvc mockMvc;
  @MockBean private TraService traService;
  @MockBean private TraHistoryService traHistoryService;
  @MockBean private PrintSettingsService printSettingsService;
  @MockBean private JwtIssuerService jwtIssuerService;
  @MockBean private IdentityDetailService identityDetailsService;
  @MockBean private AccessTokenService accessTokenService;
  private TraMapper traMapper = TraMapper.INSTANCE;

  public JwtDetails getMockJwtDetails() {
    JwtDetails jwtDetails =
        (JwtDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    return jwtDetails;
  }

  @Test
  @WithMockJWTDetails
  public void getTraById_WithValidArgs_ReturnsTraRead() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Tra tra = TraMother.aSavedTra().build();
    TraRead traRead = traMapper.traToTRARead(tra);

    given(traService.getTraById(tra.getId(), jwtDetails.getTenantId())).willReturn(tra);

    ResultActions result =
        mockMvc.perform(get("/v1/tras/{id}", tra.getId()).contentType(MediaType.APPLICATION_JSON));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(traRead));
  }

  @Test
  @WithMockJWTDetails
  public void createTra_WithValidArgs_SavesTraAndReturnsTraRead() throws Exception {
    TraCreate traCreate = TraCreateBuilder.aValidTraCreate().build();
    Tra savedTra = TraMother.aSavedTra().build();
    TraRead traRead = traMapper.traToTRARead(savedTra);

    given(traService.createTra(any(Tra.class))).willReturn(savedTra);
    String json = mapper.writeValueAsString(traCreate);

    ResultActions result =
        mockMvc.perform(post("/v1/tras").contentType(MediaType.APPLICATION_JSON).content(json));

    result
        .andExpect(status().isCreated())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(traRead));
  }

  @Test
  @WithMockJWTDetails
  public void getTRAHistoryById_WithValidArgs_ReturnsTraChanges() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    TraRead oldTra = TraReadBuilder.anEmptyTraRead().withId(1L).build();
    TraRead newTra = TraReadBuilder.anEmptyTraRead().withId(1L).withSid(2L).build();
    TraChange traChange = new TraChange();
    traChange.setOldEntity(oldTra);
    traChange.setNewEntity(newTra);
    traChange.setAt(10000L);
    traChange.setBy(UserDisplayBuilder.aValidUserDisplay().build());

    given(traHistoryService.getHistory(oldTra.getId(), jwtDetails.getTenantId()))
        .willReturn(List.of(traChange));

    ResultActions result =
        mockMvc.perform(
            get("/v1/tras/{id}/history", oldTra.getId()).contentType(MediaType.APPLICATION_JSON));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(List.of(traChange)));
  }

  @Test
  @WithMockJWTDetails
  public void updateTraById_withNewTra_UpdatesTra() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Tra tra = TraMother.aSavedTra().build();
    TraUpdate traUpdate = TraUpdateBuilder.aValidTraUpdate().build();

    given(traService.getTraById(tra.getId(), jwtDetails.getTenantId())).willReturn(tra);
    given(traService.updateTra(any(Tra.class))).willReturn(tra);

    String json = mapper.writeValueAsString(traUpdate);

    ResultActions result =
        mockMvc.perform(
            put("/v1/tras/{id}", tra.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(json));

    verify(traService).updateTra(tra);
    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody().containsObjectAsJson(traMapper.traToTRARead(tra)));
  }

  @Test
  @WithMockJWTDetails
  public void getTraList_WitAllArgs_ReturnsTraReadPaginatedResult() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Tra oldTra = TraMother.aSavedTra().withId(1L).build();
    Tra newTra = TraMother.aSavedTra().withId(2L).withSid(2L).build();
    List<Tra> list = List.of(oldTra, newTra);
    Pageable pageable = PageRequest.of(0, 20);
    Page<Tra> page = new PageImpl<>(list, pageable, list.size());

    Long groupId = 1L;
    Long ancestorGroupId = 2L;
    Long createdBy = 3L;
    WorkPermitStatus statusNot = WorkPermitStatus.CLOSED;
    Boolean hasWorkPermit = false;
    Long dateGte = 1000L;
    Long dateLte = 2000L;
    String search = "hello search";
    String filter = "createdBy=4|createdBy=5";
    Long pageNumber = 0L;
    Long pageSize = 25L;

    given(
            traService.getAllTra(
                jwtDetails.getTenantId(),
                groupId,
                ancestorGroupId,
                createdBy,
                statusNot,
                hasWorkPermit,
                dateLte,
                dateGte,
                search,
                filter,
                null,
                pageNumber,
                pageSize))
        .willReturn(page);

    ResultActions result =
        mockMvc.perform(
            get("/v1/tras")
                .queryParam("groupId", groupId.toString())
                .queryParam("ancestorGroupId", ancestorGroupId.toString())
                .queryParam("createdBy", createdBy.toString())
                .queryParam("workPermitStatusNot", statusNot.toString())
                .queryParam("hasWorkPermit", hasWorkPermit.toString())
                .queryParam("dateLte", dateLte.toString())
                .queryParam("dateGte", dateGte.toString())
                .queryParam("filter", filter)
                .queryParam("search", search)
                .queryParam("pageNumber", pageNumber.toString())
                .queryParam("pageSize", pageSize.toString())
                .contentType(MediaType.APPLICATION_JSON));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(traMapper.paginatedTRAsToPaginatedTRAListReads(page)));
  }

  @Test
  @WithMockJWTDetails
  public void deleteTraById_TraFound_DeletesTra() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Tra tra = TraMother.aSavedTra().build();

    given(traService.getTraById(tra.getId(), jwtDetails.getTenantId())).willReturn(tra);

    ResultActions result =
        mockMvc.perform(
            delete("/v1/tras/{id}", tra.getId()).contentType(MediaType.APPLICATION_JSON));

    verify(traService).deleteTra(tra);
    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void generateTraPdf_TraFound_GeneratesPdf() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    Tra tra = TraMother.aSavedTra().build();
    PrintSettings printSettings = PrintSettingsBuilder.aValidPrintSettings().build();

    given(traService.getTraById(tra.getId(), jwtDetails.getTenantId())).willReturn(tra);
    given(printSettingsService.getPrintSettingsById(jwtDetails.getTenantId()))
        .willReturn(printSettings);

    ResultActions result =
        mockMvc.perform(
            get("/v1/tras/{id}/pdf", tra.getId()).contentType(MediaType.APPLICATION_PDF_VALUE));

    verify(traService).generateTraPdf(eq(tra), eq(printSettings), any(OutputStream.class));
    result.andExpect(status().isOk());
  }
}
