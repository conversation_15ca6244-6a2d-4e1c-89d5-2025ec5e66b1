package com.vinkey.restapi.permittowork.workpermit;

import static org.assertj.core.api.BDDAssertions.then;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.vinkey.restapi.common.datetime.DateTools;
import com.vinkey.restapi.common.file.builder.FileMother;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.process.builder.ProcessInstanceCustomMother;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.group.builder.GroupMother;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantMother;
import com.vinkey.restapi.location.Location;
import com.vinkey.restapi.location.LocationService;
import com.vinkey.restapi.location.builder.LocationMother;
import com.vinkey.restapi.permittowork.tra.Tra;
import com.vinkey.restapi.permittowork.tra.TraService;
import com.vinkey.restapi.permittowork.tra.builder.TraMother;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitMother;
import com.vinkey.restapi.permittowork.workpermit.measure.WorkPermitMeasureRepository;
import com.vinkey.restapi.permittowork.workpermit.notification.WorkPermitNotificationQueuer;
import com.vinkey.restapi.permittowork.workpermit.notification.event.WorkPermitUpdatedEvent;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.workmethod.WorkPermitWorkMethodRepository;
import java.io.ByteArrayOutputStream;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ExecutionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.IContext;

@ExtendWith(MockitoExtension.class)
public class WorkPermitServiceTest {
  @InjectMocks private WorkPermitService workPermitService;
  @Mock private WorkPermitRepository workPermitRepository;
  @Mock private WorkPermitNotificationQueuer workPermitNotificationService;
  @Mock private WorkPermitWorkMethodRepository workPermitWorkMethodRepository;
  @Mock private WorkPermitMeasureRepository workPermitMeasureRepository;
  @Mock private ApplicationEventPublisher applicationEventPublisher;
  @Mock private TraService traService;
  @Mock private LocationService locationService;
  @Mock private RuntimeService runtimeService;
  @Mock private ProcessInstance processInstance;
  @Mock private TaskService taskServiceCustom;
  @Mock private DateTools dateTools;
  @Mock private ITemplateEngine templateEngine;

  public void compareWorkPermits(WorkPermit expected, WorkPermit result) {
    then(result.getActualWorkerCount()).isEqualTo(expected.getActualWorkerCount());
    then(result.getAdditionalRequirements()).isEqualTo(expected.getAdditionalRequirements());
    then(result.getAtexZone()).isEqualTo(expected.getAtexZone());
    then(result.getCreatedBy().getId()).isEqualTo(expected.getCreatedBy().getId());
    then(result.getCreationDate()).isEqualTo(expected.getCreationDate());
    then(result.getDescription()).isEqualTo(expected.getDescription());
    then(result.getEndTime()).isEqualTo(expected.getEndTime());
    then(result.getEquipment()).isEqualTo(expected.getEquipment());
    then(result.getFiles()).isEqualTo(expected.getFiles());
    then(result.getGroup().getId()).isEqualTo(expected.getGroup().getId());
    then(result.getHolder()).isEqualTo(expected.getHolder());
    then(result.getModifiedDate()).isEqualTo(expected.getModifiedDate());
    then(result.getModifiedBy().getId()).isEqualTo(expected.getModifiedBy().getId());
    then(result.getId()).isEqualTo(expected.getId());
    then(result.getLocation().getId()).isEqualTo(expected.getLocation().getId());
    then(result.getLocationGroup().getId()).isEqualTo(expected.getLocationGroup().getId());
    then(result.getLocked()).isEqualTo(expected.getLocked());
    then(result.getName()).isEqualTo(expected.getName());
    then(result.getProcessInstance().getId()).isEqualTo(expected.getProcessInstance().getId());
    then(result.getRiskCategory()).isEqualTo(expected.getRiskCategory());
    then(result.getSid()).isEqualTo(expected.getSid());
    then(result.getStartTime()).isEqualTo(expected.getStartTime());
    then(result.getStatus()).isEqualTo(expected.getStatus());
    then(result.getTenant().getId()).isEqualTo(expected.getTenant().getId());
    then(result.getTools()).isEqualTo(expected.getTools());
    Long resultTraId = result.getTra() != null ? result.getTra().getId() : null;
    Long expectedTraId = expected.getTra() != null ? expected.getTra().getId() : null;
    then(resultTraId).isEqualTo(expectedTraId);
    then(result.getWorkMethods()).isEqualTo(expected.getWorkMethods());
    then(result.getWorkerCount()).isEqualTo(expected.getWorkerCount());
  }

  @Test
  public void createWorkPermit_withWorkPermit_withTra_returnsWorkPermit() {
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    Group group = GroupMother.aSavedGroup().build();
    Location location = LocationMother.aSavedLocation().build();
    location.setOwner(group);
    Tra tra = TraMother.aSavedTra().build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withTra(tra)
            .withGroup(group)
            .withLocation(location)
            .build();

    given(
            locationService.getLocation(
                workPermit.getLocation().getId(), workPermit.getTenant().getId()))
        .willReturn(location);
    given(locationService.getOwner(location)).willReturn(location);
    given(traService.attachTraToWorkPermit(tra.getId(), workPermit)).willReturn(tra);
    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(instanceBuilder.start()).willReturn(processInstance);

    Long result = workPermitService.createWorkPermit(workPermit);

    verify(workPermitRepository).save(workPermit);
    then(result).isEqualTo(workPermit.getId());
  }

  @Test
  public void createWorkPermit_withWorkPermitNoTra_returnsWorkPermitId() {
    Group group = GroupMother.aSavedGroup().build();
    Location location = LocationMother.aSavedLocation().build();
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    location.setOwner(group);
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withTra(null)
            .withGroup(group)
            .withLocation(location)
            .build();

    given(
            locationService.getLocation(
                workPermit.getLocation().getId(), workPermit.getTenant().getId()))
        .willReturn(location);
    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(instanceBuilder.start()).willReturn(processInstance);
    given(locationService.getOwner(location)).willReturn(location);

    Long result = workPermitService.createWorkPermit(workPermit);

    verify(workPermitRepository).save(workPermit);
    // compareWorkPermits(workPermit, result);
    then(result).isEqualTo(workPermit.getId());
  }

  @Test
  public void getWorkPermit_withValidIds_returnsWorkPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    WorkPermit result =
        workPermitService.getWorkPermit(workPermit.getId(), workPermit.getTenant().getId());

    compareWorkPermits(workPermit, result);
  }

  @Test
  public void getWorkPermit_withInvalidIds_returnsNoSuchElementException() {
    RuntimeException exception =
        assertThrows(RuntimeException.class, () -> workPermitService.getWorkPermit(1L, 2L));

    then(exception.getMessage()).isEqualTo("Work Permit not found");
  }

  @Test
  public void retrieveFilterKeyValue_withKeyAndFilter_returnsLong() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria1.getValue()).willReturn("123");
    given(criteria2.getKey()).willReturn("key1");
    given(criteria2.getValue()).willReturn("456");

    Long result = workPermitService.retrieveFilterKeyValue(decodedFilter, "key1");

    then(result).isEqualTo(123L);
  }

  @Test
  public void retrieveFilterKeyValue_withourFilter_returnsNull() {
    Long result = workPermitService.retrieveFilterKeyValue(null, "key1");

    then(result).isNull();
    ;
  }

  @Test
  public void retrieveFilterKeyValue_withWrongKeyAndFilter_returnsNull() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria2.getKey()).willReturn("key1");

    Long result = workPermitService.retrieveFilterKeyValue(decodedFilter, "key2");

    then(result).isNull();
  }

  @Test
  public void getWorkPermits_withValidArgs_returnsPage() {
    WorkPermit workPermit1 = WorkPermitMother.aSavedWorkPermit().build();
    WorkPermit workPermit2 = WorkPermitMother.aSavedWorkPermit().but().withId(88L).build();
    Long tenantId = 1L;
    Long groupId = 4L;
    Long locationOwner = 3L;
    Long locationOwnerAncestor = 6L;
    WorkPermitStatus status = WorkPermitStatus.ISSUED;
    Long createdBy = 8L;
    Long lototoPlan = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long excludeUser = 9L;
    List<Long> candidateUsers = List.of(1L, 2L, 3L, 4L, 5L);
    Long ancestorGroupId = 55L;
    Long creationDateGte = 123123L;
    Long creationDateLte = 234234L;
    WorkPermitStatus statusNot = WorkPermitStatus.APPROVED;
    RiskCategory riskCategory = RiskCategory.HIGH;
    String search = "Lifting permit";
    Long pageSize = 10L;
    Long pageNumber = 0L;
    String filter = null;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    List<WorkPermit> list = Arrays.asList(workPermit1, workPermit2);
    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(WorkPermit_.ID).descending());
    Page<WorkPermit> page = new PageImpl<>(list, pageable, list.size());

    given(workPermitRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(page);

    Page<WorkPermit> result =
        workPermitService.getWorkPermits(
            tenantId,
            groupId,
            status,
            statusNot,
            riskCategory,
            workMethodIds,
            createdBy,
            lototoPlan,
            creationDateGte,
            creationDateLte,
            search,
            filter,
            null,
            pageSize,
            pageNumber,
            candidateGroups,
            candidateUsers,
            ancestorGroupId,
            locationOwner,
            locationOwnerAncestor,
            (Long) null, // locationId
            (Long) null, // ancestorLocationId
            excludeUser);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareWorkPermits(result.getContent().get(0), page.getContent().get(0));
    compareWorkPermits(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void getWorkPermits_withNullPageArgs_returnsError() {
    Long tenantId = 1L;
    Long groupId = 4L;
    Long locationOwner = 3L;
    Long locationOwnerAncestor = 6L;
    WorkPermitStatus status = WorkPermitStatus.ISSUED;
    Long createdBy = 8L;
    Long lototoPlan = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long excludeUser = 9L;
    List<Long> candidateUsers = List.of(1L, 2L, 3L, 4L, 5L);
    Long ancestorGroupId = 55L;
    Long creationDateGte = 123123L;
    Long creationDateLte = 234234L;
    WorkPermitStatus statusNot = WorkPermitStatus.APPROVED;
    RiskCategory riskCategory = RiskCategory.HIGH;
    String search = "Lifting permit";
    Long pageSize = null;
    Long pageNumber = null;
    String filter = null;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    MissingRequestPropertiesException exception =
        assertThrows(
            MissingRequestPropertiesException.class,
            () ->
                workPermitService.getWorkPermits(
                    tenantId,
                    groupId,
                    status,
                    statusNot,
                    riskCategory,
                    workMethodIds,
                    createdBy,
                    lototoPlan,
                    creationDateGte,
                    creationDateLte,
                    search,
                    filter,
                    null,
                    pageSize,
                    pageNumber,
                    candidateGroups,
                    candidateUsers,
                    ancestorGroupId,
                    locationOwner,
                    locationOwnerAncestor,
                    (Long) null, // locationId
                    (Long) null, // ancestorLocationId
                    excludeUser));

    then(exception.getMessage()).isEqualTo("Page size and page number are required");
  }

  @Test
  public void getWorkPermits_withNullPageSize_returnsError() {
    Long tenantId = 1L;
    Long groupId = 4L;
    Long locationOwner = 3L;
    Long locationOwnerAncestor = 6L;
    WorkPermitStatus status = WorkPermitStatus.ISSUED;
    Long createdBy = 8L;
    Long lototoPlan = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long excludeUser = 9L;
    List<Long> candidateUsers = List.of(1L, 2L, 3L, 4L, 5L);
    Long ancestorGroupId = 55L;
    Long creationDateGte = 123123L;
    Long creationDateLte = 234234L;
    WorkPermitStatus statusNot = WorkPermitStatus.APPROVED;
    RiskCategory riskCategory = RiskCategory.HIGH;
    String search = "Lifting permit";
    Long pageSize = null;
    Long pageNumber = 10L;
    String filter = null;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    MissingRequestPropertiesException exception =
        assertThrows(
            MissingRequestPropertiesException.class,
            () ->
                workPermitService.getWorkPermits(
                    tenantId,
                    groupId,
                    status,
                    statusNot,
                    riskCategory,
                    workMethodIds,
                    createdBy,
                    lototoPlan,
                    creationDateGte,
                    creationDateLte,
                    search,
                    filter,
                    null,
                    pageSize,
                    pageNumber,
                    candidateGroups,
                    candidateUsers,
                    ancestorGroupId,
                    locationOwner,
                    locationOwnerAncestor,
                    (Long) null, // locationId
                    (Long) null, // ancestorLocationId
                    excludeUser));

    then(exception.getMessage()).isEqualTo("Page size and page number are required");
  }

  @Test
  public void getWorkPermits_withNullPageNumber_returnsError() {
    Long tenantId = 1L;
    Long groupId = 4L;
    Long locationOwner = 3L;
    Long locationOwnerAncestor = 6L;
    WorkPermitStatus status = WorkPermitStatus.ISSUED;
    Long createdBy = 8L;
    Long lototoPlan = 10L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long excludeUser = 9L;
    List<Long> candidateUsers = List.of(1L, 2L, 3L, 4L, 5L);
    Long ancestorGroupId = 55L;
    Long creationDateGte = 123123L;
    Long creationDateLte = 234234L;
    WorkPermitStatus statusNot = WorkPermitStatus.APPROVED;
    RiskCategory riskCategory = RiskCategory.HIGH;
    String search = "Lifting permit";
    Long pageSize = 0L;
    Long pageNumber = null;
    String filter = null;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    MissingRequestPropertiesException exception =
        assertThrows(
            MissingRequestPropertiesException.class,
            () ->
                workPermitService.getWorkPermits(
                    tenantId,
                    groupId,
                    status,
                    statusNot,
                    riskCategory,
                    workMethodIds,
                    createdBy,
                    lototoPlan,
                    creationDateGte,
                    creationDateLte,
                    search,
                    filter,
                    null,
                    pageSize,
                    pageNumber,
                    candidateGroups,
                    candidateUsers,
                    ancestorGroupId,
                    locationOwner,
                    locationOwnerAncestor,
                    (Long) null, // locationId
                    (Long) null, // ancestorLocationId
                    excludeUser));

    then(exception.getMessage()).isEqualTo("Page size and page number are required");
  }

  @Test
  public void updateWorkPermit_withLockedWorkPermit_throwsLockedException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    workPermit.setLocked(true);

    assertThrows(PermitLockedException.class, () -> workPermitService.updateWorkPermit(workPermit));
  }

  @Test
  public void updateWorkPermit_withUnlockedAndDifferentRiskCategory_updatesWorkPermit() {
    Group group = GroupMother.aSavedGroup().build();
    Location location = LocationMother.aSavedLocation().build();
    location.setOwner(group);
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withLocation(location)
            .withLocationGroup(group)
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.updateWorkPermit(workPermit);
    verify(runtimeService)
        .setVariable(workPermit.getProcessInstance().getId(), "riskcategory", "Low");
  }

  @Test
  public void updateWorkPermit_withDifferentNames_updatesNames() {
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit().but().withRiskCategory(RiskCategory.LOW).build();
    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withName("Old Name")
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));

    workPermitService.updateWorkPermit(workPermit);
    verify(runtimeService)
        .setVariable(workPermit.getProcessInstance().getId(), "name", workPermit.getName());
  }

  @Test
  public void updateWorkPermitTra_withTraAdded_updatesWorkPermit() {
    Tra tra = TraMother.aSavedTra().build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(tra)
            .build();
    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(null)
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));
    given(traService.attachTraToWorkPermit(tra.getId(), workPermit)).willReturn(tra);

    workPermitService.updateWorkPermit(workPermit);

    verify(traService).attachTraToWorkPermit(tra.getId(), workPermit);
    then(workPermit.getTra().getId()).isEqualTo(tra.getId());
  }

  @Test
  public void updateWorkPermitTra_withBothTraNull_updatesPermit() {
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(null)
            .build();
    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(null)
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));

    workPermitService.updateWorkPermit(workPermit);

    then(workPermit.getTra()).isNull();
  }

  @Test
  public void updateWorkPermit_withDifferentGroupIds_returnsWorkPermit() {
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit().but().withRiskCategory(RiskCategory.LOW).build();
    workPermit.setProcessInstance(
        ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().withId("id").build());

    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withLocation(LocationMother.aSavedLocation().withId(99882223311L).build())
            .build();

    Group group = GroupMother.aSavedGroup().build();
    Location location = LocationMother.aSavedLocation().withOwner(group).build();
    ExecutionQuery executionQuery = mock(ExecutionQuery.class);
    Execution execution = mock(Execution.class);
    ChangeActivityStateBuilder builder = mock(ChangeActivityStateBuilder.class);

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));
    given(
            locationService.getLocation(
                workPermit.getLocation().getId(), workPermit.getTenant().getId()))
        .willReturn(location);
    given(locationService.getOwner(location)).willReturn(location);
    given(runtimeService.createExecutionQuery()).willReturn(executionQuery);
    given(executionQuery.processInstanceId("id")).willReturn(executionQuery);
    given(executionQuery.list()).willReturn(List.of(execution));
    given(execution.getActivityId()).willReturn("activityId");
    given(runtimeService.createChangeActivityStateBuilder()).willReturn(builder);

    workPermitService.updateWorkPermit(workPermit);

    verify(applicationEventPublisher).publishEvent(new WorkPermitUpdatedEvent(workPermit.getId()));
  }

  @Test
  public void updateWorkPermitTra_withTraReplaced_updatesWorkPermit() {
    Tra oldTra = TraMother.aSavedTra().withId(66L).build();
    Tra newTra = TraMother.aSavedTra().withId(56744L).build();
    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(oldTra)
            .build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(newTra)
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));
    given(traService.detachTraFromWorkPermit(oldTra.getId())).willReturn(oldTra);
    given(traService.attachTraToWorkPermit(newTra.getId(), workPermit)).willReturn(newTra);

    workPermitService.updateWorkPermit(workPermit);

    verify(traService).detachTraFromWorkPermit(oldTra.getId());
    verify(traService).attachTraToWorkPermit(newTra.getId(), workPermit);
    then(workPermit.getTra().getId()).isEqualTo(newTra.getId());
  }

  @Test
  public void updateWorkPermitTra_withTraRemoved_updatesWorkPermit() {
    Tra oldTra = TraMother.aSavedTra().withId(66L).build();
    WorkPermit oldPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(oldTra)
            .build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withTra(null)
            .build();

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(oldPermit));
    given(traService.detachTraFromWorkPermit(oldTra.getId())).willReturn(oldTra);

    workPermitService.updateWorkPermit(workPermit);

    verify(traService).detachTraFromWorkPermit(oldTra.getId());
    then(workPermit.getTra()).isNull();
  }

  @Test
  public void cancelWorkPermit_withWorkPermit_cancelsWorkPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    workPermit.setLocked(true);
    workPermitService.cancelWorkPermit(workPermit);

    verify(workPermitRepository).save(workPermit);
    verify(runtimeService)
        .deleteProcessInstance(
            workPermit.getProcessInstance().getId(), "Work Permit canceled by user");
    then(workPermit.getStatus()).isEqualTo(WorkPermitStatus.CANCELED);
    then(workPermit.getLocked()).isTrue();
  }

  @Test
  public void prepareWorkPermit_withWorkPermit_preparesWorkPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.prepareWorkPermit(workPermit.getId(), workPermit.getTenant().getId());
    verify(workPermitRepository).save(workPermit);
    then(workPermit.getStatus()).isEqualTo(WorkPermitStatus.PREPARED);
  }

  @Test
  public void closeWorkPermit_withWorkPermit_closesWorkPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.closeWorkPermit(workPermit.getId(), workPermit.getTenant().getId());

    verify(workPermitRepository).save(workPermit);
    then(workPermit.getStatus()).isEqualTo(WorkPermitStatus.CLOSED);
  }

  @Test
  public void lockWorkPermit_withWorkPermitIds_locksPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();

    Map<String, Object> variables = new HashMap<String, Object>();

    Long startTimeWithoutMS = workPermit.getStartTime() / 1000;

    Instant instant = Instant.ofEpochSecond(startTimeWithoutMS - 10800);
    String iso8601Date = instant.toString();
    variables.put("startTimeMinusThree", iso8601Date);

    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.lockWorkPermit(workPermit.getId(), workPermit.getTenant().getId());

    verify(runtimeService)
        .setVariables(workPermit.getProcessInstance().getProcessInstanceId(), variables);
    verify(workPermitRepository).save(workPermit);
    then(workPermit.getLocked()).isTrue();
  }

  @Test
  public void issueWorkPermit_withArgs_issuesWorkPermit() {
    Long id = 1L;
    Long tenantId = 10L;
    String holder = "John Doe";
    String additionalRequirements = "Safety training";
    Long actualWorkerCount = 5L;
    WorkPermit result =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withAdditionalRequirements(additionalRequirements)
            .withHolder(holder)
            .withActualWorkerCount(actualWorkerCount)
            .withId(id)
            .withTenant(TenantMother.aSavedTenant().withId(tenantId).build())
            .build();
    result.setStatus(WorkPermitStatus.ISSUED);
    WorkPermit workPermit = result;
    workPermit.setStatus(null);

    given(workPermitRepository.findByIdAndTenantId(1L, 10L)).willReturn(Optional.of(workPermit));

    workPermitService.issueWorkPermit(
        id, tenantId, holder, additionalRequirements, actualWorkerCount);

    verify(workPermitRepository).save(any(WorkPermit.class));
    compareWorkPermits(workPermit, result);
    then(workPermit.getStatus()).isEqualTo(WorkPermitStatus.ISSUED);
  }

  @Test
  public void approveWorkPermit_withIds_approvesWorkPermit() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.approveWorkPermit(workPermit.getId(), workPermit.getTenant().getId());

    verify(workPermitRepository).save(workPermit);
    then(workPermit.getStatus()).isEqualTo(WorkPermitStatus.APPROVED);
  }

  @Test
  public void setWorkPermitRiskCategory_withRiskCategory_setsRisk() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    given(
            workPermitRepository.findByIdAndTenantId(
                workPermit.getId(), workPermit.getTenant().getId()))
        .willReturn(Optional.of(workPermit));

    workPermitService.setWorkPermitRiskCategory(
        workPermit.getId(), workPermit.getTenant().getId(), RiskCategory.HIGH);

    verify(workPermitRepository).save(workPermit);
    then(workPermit.getRiskCategory()).isEqualTo(RiskCategory.HIGH);
  }

  @Test
  public void generateWorkPermit_withArgs_returnsOutput() {
    Tra tra = TraMother.aSavedTra().build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .withTra(tra)
            .withProcessInstance(
                ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build())
            .withRiskCategory(RiskCategory.LOW)
            .build();
    String timeZone = "UTC";
    boolean isCopy = false;
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PrintSettings printSettings = mock(PrintSettings.class);

    Timestamp currentDate = Timestamp.from(Instant.now());
    TaskHistory task1 = new TaskHistory();
    task1.setName("Process feedback");
    task1.setTaskDefinitionKey("key1");
    TaskHistory task2 = new TaskHistory();
    task2.setName("Process feedback");
    task2.setTaskDefinitionKey("key1");
    task2.setEndTime(currentDate);
    TaskHistory task3 = new TaskHistory();
    task3.setName("Revoke");
    task3.setTaskDefinitionKey("key3");
    task3.setEndTime(currentDate);
    TaskHistory task4 = new TaskHistory();
    task4.setName("DE-LOTOTO");
    task4.setTaskDefinitionKey("key4");
    task4.setEndTime(currentDate);
    TaskHistory task5 = new TaskHistory();
    task5.setName("Start closing");
    task5.setTaskDefinitionKey("key5");
    task5.setEndTime(currentDate);
    TaskHistory task6 = new TaskHistory();
    task6.setName("Issue");
    task6.setTaskDefinitionKey("key6");
    task6.setEndTime(currentDate);
    TaskHistory task7 = new TaskHistory();
    task7.setName("Validate");
    task7.setTaskDefinitionKey("key7");
    task7.setEndTime(currentDate);
    List<TaskHistory> historyTasks = List.of(task1, task2, task3, task4, task5, task6, task7);

    given(taskServiceCustom.getHistory(any(String.class), any(Long.class)))
        .willReturn(historyTasks);
    given(traService.getTraById(tra.getId(), tra.getTenant().getId()))
        .willReturn(tra); // Adjust as needed
    given(templateEngine.process(eq("pdf/work-permit.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    workPermitService.generateWorkPermit(workPermit, printSettings, timeZone, isCopy, outputStream);

    verify(taskServiceCustom, times(1)).getHistory(any(String.class), any(Long.class));

    byte[] generatedPdfBytes = outputStream.toByteArray();
    then(generatedPdfBytes.length).isGreaterThan(0);
  }

  @Test
  public void generateWorkPermit_withNullTra_returnsOutput() {
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .withTra(null)
            .withRiskCategory(RiskCategory.LOW)
            .withProcessInstance(
                ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build())
            .build();
    String timeZone = "UTC";
    boolean isCopy = false;
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PrintSettings printSettings = mock(PrintSettings.class);

    Timestamp currentDate = Timestamp.from(Instant.now());
    TaskHistory task1 = new TaskHistory();
    task1.setName("Process feedback");
    task1.setTaskDefinitionKey("key1");
    TaskHistory task2 = new TaskHistory();
    task2.setName("Process feedback");
    task2.setTaskDefinitionKey("key1");
    task2.setEndTime(currentDate);
    TaskHistory task3 = new TaskHistory();
    task3.setName("Revoke");
    task3.setTaskDefinitionKey("key3");
    task3.setEndTime(currentDate);
    TaskHistory task4 = new TaskHistory();
    task4.setName("DE-LOTOTO");
    task4.setTaskDefinitionKey("key4");
    task4.setEndTime(currentDate);
    TaskHistory task5 = new TaskHistory();
    task5.setName("Start closing");
    task5.setTaskDefinitionKey("key5");
    task5.setEndTime(currentDate);
    List<TaskHistory> historyTasks = List.of(task1, task2, task3, task4, task5);

    given(taskServiceCustom.getHistory(any(String.class), any(Long.class)))
        .willReturn(historyTasks);
    given(templateEngine.process(eq("pdf/work-permit.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    workPermitService.generateWorkPermit(workPermit, printSettings, timeZone, isCopy, outputStream);

    verify(taskServiceCustom, times(1)).getHistory(any(String.class), any(Long.class));

    byte[] generatedPdfBytes = outputStream.toByteArray();
    then(generatedPdfBytes.length).isGreaterThan(0);
  }

  @Test
  public void generateWorkPermit_withIssuedStatusAndCopyTrue_returnsOutput() {
    Tra tra = TraMother.aSavedTra().build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withTra(tra)
            .withRiskCategory(RiskCategory.LOW)
            .withWorkPermitStatus(WorkPermitStatus.ISSUED)
            .withProcessInstance(
                ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build())
            .build();
    String timeZone = "UTC";
    boolean isCopy = true;
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PrintSettings printSettings = mock(PrintSettings.class);

    Timestamp currentDate = Timestamp.from(Instant.now());
    TaskHistory task1 = new TaskHistory();
    task1.setName("Process feedback");
    task1.setTaskDefinitionKey("key1");
    TaskHistory task2 = new TaskHistory();
    task2.setName("Process feedback");
    task2.setTaskDefinitionKey("key1");
    task2.setEndTime(currentDate);
    TaskHistory task3 = new TaskHistory();
    task3.setName("Revoke");
    task3.setTaskDefinitionKey("key3");
    task3.setEndTime(currentDate);
    TaskHistory task4 = new TaskHistory();
    task4.setName("DE-LOTOTO");
    task4.setTaskDefinitionKey("key4");
    task4.setEndTime(currentDate);
    TaskHistory task5 = new TaskHistory();
    task5.setName("Start closing");
    task5.setTaskDefinitionKey("key5");
    task5.setEndTime(currentDate);
    TaskHistory task7 = new TaskHistory();
    task7.setName("Validate");
    task7.setTaskDefinitionKey("key6");
    task7.setEndTime(currentDate);
    List<TaskHistory> historyTasks = List.of(task1, task2, task3, task4, task5, task7);

    given(taskServiceCustom.getHistory(any(String.class), any(Long.class)))
        .willReturn(historyTasks);
    given(traService.getTraById(tra.getId(), tra.getTenant().getId()))
        .willReturn(tra); // Adjust as needed
    given(templateEngine.process(eq("pdf/work-permit.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    workPermitService.generateWorkPermit(workPermit, printSettings, timeZone, isCopy, outputStream);

    verify(taskServiceCustom, times(1)).getHistory(any(String.class), any(Long.class));

    byte[] generatedPdfBytes = outputStream.toByteArray();
    then(generatedPdfBytes.length).isGreaterThan(0);
  }

  @Test
  public void generateWorkPermit_withNotNullScreenshotUrl_returnsOutput() {
    Tra tra = TraMother.aSavedTra().build();
    ProcessInstanceCustom processInstanceCustom =
        ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build();
    WorkPermit workPermit =
        WorkPermitMother.aSavedWorkPermit()
            .but()
            .withRiskCategory(RiskCategory.LOW)
            .withProcessInstance(processInstanceCustom)
            .withTra(tra)
            .build();
    String timeZone = "UTC";
    boolean isCopy = false;
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PrintSettings printSettings = mock(PrintSettings.class);

    Timestamp currentDate = Timestamp.from(Instant.now());
    TaskHistory task1 = new TaskHistory();
    task1.setName("Process feedback");
    task1.setTaskDefinitionKey("key1");
    TaskHistory task2 = new TaskHistory();
    task2.setName("Process feedback");
    task2.setTaskDefinitionKey("key1");
    task2.setEndTime(currentDate);
    TaskHistory task3 = new TaskHistory();
    task3.setName("Revoke");
    task3.setTaskDefinitionKey("key3");
    task3.setEndTime(currentDate);
    TaskHistory task4 = new TaskHistory();
    task4.setName("DE-LOTOTO");
    task4.setTaskDefinitionKey("key4");
    task4.setEndTime(currentDate);
    TaskHistory task5 = new TaskHistory();
    task5.setName("Start closing");
    task5.setTaskDefinitionKey("key5");
    task5.setEndTime(currentDate);
    TaskHistory task6 = new TaskHistory();
    task6.setName("Issue");
    task6.setTaskDefinitionKey("key6");
    task6.setEndTime(currentDate);
    TaskHistory task7 = new TaskHistory();
    task7.setName("Validate");
    task7.setTaskDefinitionKey("key6");
    task7.setEndTime(currentDate);
    List<TaskHistory> historyTasks = List.of(task1, task2, task3, task4, task5, task6, task7);

    given(taskServiceCustom.getHistory(any(String.class), any(Long.class)))
        .willReturn(historyTasks);
    given(traService.getTraById(tra.getId(), tra.getTenant().getId()))
        .willReturn(tra); // Adjust as needed
    given(templateEngine.process(eq("pdf/work-permit.html"), any(IContext.class)))
        .willReturn("Mocked HTML");
    given(locationService.getLocationScreenshotUrl(any()))
        .willReturn(FileMother.aSavedFile().build());

    workPermitService.generateWorkPermit(workPermit, printSettings, timeZone, isCopy, outputStream);

    verify(taskServiceCustom, times(1)).getHistory(any(String.class), any(Long.class));

    byte[] generatedPdfBytes = outputStream.toByteArray();
    then(generatedPdfBytes.length).isGreaterThan(0);
  }

  @Test
  public void getWorkPermits_withFilterStrings_returnsPage() {
    WorkPermit workPermit1 = WorkPermitMother.aSavedWorkPermit().build();
    WorkPermit workPermit2 = WorkPermitMother.aSavedWorkPermit().but().withId(88L).build();

    Long pageSize = 10L;
    Long pageNumber = 0L;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    String filter =
        "tenantId=1|(workMethods=1,2&workMethods=3,4,5)&groupId=4&locationOwner=3&locationOwnerAncestor=6"
            + "&status=ISSUED&createdBy=8&candidateGroups=(Shell%20Group,Esso%20Group)&excludeUser=9"
            + "&candidateUsers=(1,2,3,4,5)&ancestorGroupId=55&creationDateGte=123123&creationDateGte=234234"
            + "statusNot=APPROVED&riskCategory=HIGH&search=Lifting%20permit";

    List<WorkPermit> list = Arrays.asList(workPermit1, workPermit2);
    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(WorkPermit_.ID).descending());
    Page<WorkPermit> page = new PageImpl<>(list, pageable, list.size());

    given(workPermitRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(page);

    Page<WorkPermit> result =
        workPermitService.getWorkPermits(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            filter,
            null,
            pageSize,
            pageNumber,
            null,
            null,
            null,
            null,
            null,
            (Long) null, // locationId
            (Long) null, // ancestorLocationId
            null);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    then(result.getContent().get(0)).isNotNull();
    then(result.getContent().get(1)).isNotNull();
    compareWorkPermits(result.getContent().get(0), page.getContent().get(0));
    compareWorkPermits(result.getContent().get(1), page.getContent().get(1));
  }
}
