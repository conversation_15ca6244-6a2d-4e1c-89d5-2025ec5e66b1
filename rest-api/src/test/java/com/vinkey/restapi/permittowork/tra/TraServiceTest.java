package com.vinkey.restapi.permittowork.tra;

import static org.assertj.core.api.BDDAssertions.then;
import static org.assertj.core.api.BDDAssertions.thenThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;

import com.vinkey.restapi.permittowork.tra.builder.TraMother;
import com.vinkey.restapi.permittowork.tra.row.TraRow;
import com.vinkey.restapi.permittowork.tra.row.TraRowRepository;
import com.vinkey.restapi.permittowork.tra.row.builder.TraRowBuilder;
import com.vinkey.restapi.permittowork.workpermit.WorkPermit;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitStatus;
import com.vinkey.restapi.permittowork.workpermit.builder.WorkPermitMother;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.builder.PrintSettingsBuilder;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.IContext;

@ExtendWith(MockitoExtension.class)
public class TraServiceTest {

  @InjectMocks private TraService traService;
  @Mock private TraRepository traRepository;
  @Mock private TraRowRepository traRowRepository;
  @Mock private ITemplateEngine templateEngine;

  public void compareResult(Tra tra, Tra result) {
    then(result.getId()).isEqualTo(tra.getId());
    then(result.getSid()).isEqualTo(tra.getSid());
    then(result.getName()).isEqualTo(tra.getName());
    then(result.getTenant().getId()).isEqualTo(tra.getTenant().getId());
    then(result.getGroup().getId()).isEqualTo(tra.getGroup().getId());
    then(result.getParticipants()).isEqualTo(tra.getParticipants());
    then(result.getCreatedBy().getId()).isEqualTo(tra.getCreatedBy().getId());
    then(result.getCreationDate()).isEqualTo(tra.getCreationDate());
    then(result.getModifiedBy().getId()).isEqualTo(tra.getModifiedBy().getId());
    then(result.getModifiedDate()).isEqualTo(tra.getModifiedDate());
    then(result.getWorkPermit()).isEqualTo(tra.getWorkPermit());
    then(result.getTraRows().size()).isEqualTo(tra.getTraRows().size());
  }

  @Test
  public void getTraById_TraAndTraRowsFound_ReturnsTra() {
    Tra tra = TraMother.aSavedTra().withTraRows(new ArrayList<>()).build();
    List<TraRow> traRows = List.of(TraRowBuilder.aValidRow().withTra(tra).build());

    given(traRepository.findByIdAndTenantId(tra.getId(), tra.getTenant().getId()))
        .willReturn(Optional.of(tra));
    given(traRowRepository.findAllByTraID(tra.getId())).willReturn(traRows);

    Tra result = traService.getTraById(tra.getId(), tra.getTenant().getId());

    then(result).isEqualTo(tra);
    then(result.getTraRows()).isEqualTo(traRows);
  }

  @Test
  public void getTraById_TraNotFound_ThrowsException() {
    Tra tra = TraMother.aSavedTra().build();

    given(traRepository.findByIdAndTenantId(tra.getId(), tra.getTenant().getId()))
        .willReturn(Optional.empty());

    thenThrownBy(() -> traService.getTraById(tra.getId(), tra.getTenant().getId()));
  }

  @Test
  public void attachTraToWorkPermit_NullWorkPermit_ThrowsException() {
    Tra tra = TraMother.aSavedTra().build();

    thenThrownBy(() -> traService.attachTraToWorkPermit(tra.getId(), null));
  }

  @Test
  public void attachTraToWorkPermit_NullTraId_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();

    thenThrownBy(() -> traService.attachTraToWorkPermit(null, workPermit));
  }

  @Test
  public void attachTraToWorkPermit_TraNotFound_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.empty());

    thenThrownBy(() -> traService.attachTraToWorkPermit(tra.getId(), workPermit));
  }

  @Test
  public void attachTraToWorkPermit_TraAlreadyAttachedToWorkPermit_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.of(tra));

    thenThrownBy(() -> traService.attachTraToWorkPermit(tra.getId(), workPermit));
  }

  @Test
  public void attachTraToWorkPermit_ValidArgs_AttachesSavesAndReturnsTra() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().withTra(null).build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(null).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.of(tra));
    given(traRepository.save(tra)).willReturn(tra);

    Tra result = traService.attachTraToWorkPermit(tra.getId(), workPermit);

    then(result).isEqualTo(tra);
    then(result.getWorkPermit()).isEqualTo(workPermit);
  }

  @Test
  public void detachTraFromWorkPermit_NullTraId_ThrowsException() {
    thenThrownBy(() -> traService.detachTraFromWorkPermit(null));
  }

  @Test
  public void detatchTraFromWorkPermit_TraNotFound_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.empty());

    thenThrownBy(() -> traService.detachTraFromWorkPermit(tra.getId()));
  }

  @Test
  public void detachTraFromWorkPermit_AlreadyDetached_StaysDetached() {
    Tra tra = TraMother.aSavedTra().withWorkPermit(null).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.of(tra));
    given(traRepository.save(tra)).willReturn(tra);

    Tra result = traService.detachTraFromWorkPermit(tra.getId());

    then(result.getWorkPermit()).isNull();
    then(result).isEqualTo(tra);
  }

  @Test
  public void detachTraFromWorkPermit_AttachedTra_DetachesTra() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    given(traRepository.findById(tra.getId())).willReturn(Optional.of(tra));
    given(traRepository.save(tra)).willReturn(tra);

    Tra result = traService.detachTraFromWorkPermit(tra.getId());

    then(result.getWorkPermit()).isNull();
    then(result).isEqualTo(tra);
  }

  @Test
  public void getAllTra_WithValidArgs_ReturnsPage() {
    Tra traOne = TraMother.aSavedTra().build();
    Tra traTwo = TraMother.aSavedTra().withId(88L).build();

    Long tenantId = 1L;
    Long groupId = 4L;
    Long createdBy = 8L;
    Long ancestorGroupId = 55L;
    Long creationDateGte = 123123L;
    Long creationDateLte = 234234L;
    Boolean hasWorkPermit = true;
    WorkPermitStatus statusNot = WorkPermitStatus.APPROVED;
    String search = "TRA for lifting";
    Long pageSize = 10L;
    Long pageNumber = 0L;
    String filter = null;

    List<Tra> list = Arrays.asList(traOne, traTwo);
    Pageable pageable =
        PageRequest.of(pageNumber.intValue(), pageSize.intValue(), Sort.by(Tra_.ID).descending());
    Page<Tra> page = new PageImpl<>(list, pageable, list.size());

    given(traRepository.findAll(any(Specification.class), any(Pageable.class))).willReturn(page);

    Page<Tra> result =
        traService.getAllTra(
            tenantId,
            groupId,
            ancestorGroupId,
            createdBy,
            statusNot,
            hasWorkPermit,
            creationDateLte,
            creationDateGte,
            search,
            filter,
            null,
            pageNumber,
            pageSize);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareResult(result.getContent().get(0), page.getContent().get(0));
    compareResult(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void getAllTra_WithAllNull_ReturnsDefaultPage() {
    Tra traOne = TraMother.aSavedTra().build();
    Tra traTwo = TraMother.aSavedTra().withId(88L).build();

    List<Tra> list = Arrays.asList(traOne, traTwo);
    Pageable pageable = PageRequest.of(0, 20, Sort.by(Tra_.ID).descending());
    Page<Tra> page = new PageImpl<>(list, pageable, list.size());

    given(traRepository.findAll(any(Specification.class), any(Pageable.class))).willReturn(page);

    Page<Tra> result =
        traService.getAllTra(
            null, null, null, null, null, null, null, null, null, null, null, null, null);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareResult(result.getContent().get(0), page.getContent().get(0));
    compareResult(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void getAllTra_WithFilterString_ReturnsDefaultPage() {
    Tra traOne = TraMother.aSavedTra().build();
    Tra traTwo = TraMother.aSavedTra().withId(88L).build();

    List<Tra> list = Arrays.asList(traOne, traTwo);
    Pageable pageable = PageRequest.of(0, 20, Sort.by(Tra_.ID).descending());
    Page<Tra> page = new PageImpl<>(list, pageable, list.size());

    given(traRepository.findAll(any(Specification.class), any(Pageable.class))).willReturn(page);

    Page<Tra> result =
        traService.getAllTra(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            "groupId=2&ancestorGroupId=3|createdBy=1",
            null,
            null,
            null);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareResult(result.getContent().get(0), page.getContent().get(0));
    compareResult(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void createTra_WithTra_SavesAndReturnsTra() {
    Tra tra = TraMother.aSavedTra().build();

    given(traRepository.save(tra)).willReturn(tra);
    given(traRepository.findByIdAndTenantId(tra.getId(), tra.getTenant().getId()))
        .willReturn(Optional.of(tra));

    Tra result = traService.createTra(tra);

    then(result).isEqualTo(tra);
  }

  @Test
  public void updateTra_AttachedToLockedWorkPermit_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().but().withLocked(true).build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    thenThrownBy(() -> traService.updateTra(tra));
  }

  @Test
  public void updateTra_AttachedToUnlockedWorkPermit_SavesAndReturnsTra() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().but().withLocked(false).build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    given(traRepository.save(tra)).willReturn(tra);
    given(traRepository.findByIdAndTenantId(tra.getId(), tra.getTenant().getId()))
        .willReturn(Optional.of(tra));

    Tra result = traService.updateTra(tra);

    then(result).isEqualTo(tra);
  }

  @Test
  public void updateTra_DetachedFromWorkPermit_SavesAndReturnsTra() {
    Tra tra = TraMother.aSavedTra().withWorkPermit(null).build();

    given(traRepository.save(tra)).willReturn(tra);
    given(traRepository.findByIdAndTenantId(tra.getId(), tra.getTenant().getId()))
        .willReturn(Optional.of(tra));

    Tra result = traService.updateTra(tra);

    then(result).isEqualTo(tra);
  }

  @Test
  public void deleteTra_AttachedToLockedWorkPermit_ThrowsException() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().but().withLocked(true).build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    thenThrownBy(() -> traService.deleteTra(tra));
  }

  @Test
  public void deleteTra_AttachedToUnlockedWorkPermit_DeletesTra() {
    WorkPermit workPermit = WorkPermitMother.aSavedWorkPermit().but().withLocked(false).build();
    Tra tra = TraMother.aSavedTra().withWorkPermit(workPermit).build();

    traService.deleteTra(tra);

    verify(traRepository).delete(tra);
  }

  @Test
  public void deleteTra_DetachedFromWorkPermit_DeletesTra() {
    Tra tra = TraMother.aSavedTra().withWorkPermit(null).build();

    traService.deleteTra(tra);

    verify(traRepository).delete(tra);
  }

  @Test
  public void generateTraPdf_ValidArgs_GeneratesTemplate() {
    Tra tra = TraMother.aSavedTra().withWorkPermit(null).build();
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    PrintSettings printSettings = PrintSettingsBuilder.aValidPrintSettings().build();

    given(templateEngine.process(eq("pdf/tra.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    traService.generateTraPdf(tra, printSettings, outputStream);

    then(outputStream.toByteArray().length).isGreaterThan(0);
  }
}
