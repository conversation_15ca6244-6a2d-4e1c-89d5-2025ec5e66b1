package com.vinkey.restapi.flowable.task;

import static org.assertj.core.api.BDDAssertions.then;

import com.vinkey.restapi.PersistenceTest;
import com.vinkey.restapi.flowable.HistoryDetailsCustom;
import com.vinkey.restapi.flowable.builders.HistoryDetailCustomMother;
import com.vinkey.restapi.flowable.builders.IdentityLinkCustomMother;
import com.vinkey.restapi.flowable.builders.VariableMother;
import com.vinkey.restapi.flowable.identitylink.IdentityLinkCustom;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.process.builder.ProcessInstanceCustomMother;
import com.vinkey.restapi.flowable.task.builder.TaskCustomBuilder;
import com.vinkey.restapi.flowable.task.builder.TaskCustomMother;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.flowable.task.history.TaskHistoryRepository;
import com.vinkey.restapi.flowable.variable.Variable;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.group.builder.GroupMother;
import com.vinkey.restapi.identityandaccess.identity.Identity;
import com.vinkey.restapi.identityandaccess.identity.builder.IdentityBuilder;
import com.vinkey.restapi.identityandaccess.identity.builder.IdentityMother;
import com.vinkey.restapi.identityandaccess.tenant.Tenant;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantBuilder;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantMother;
import com.vinkey.restapi.identityandaccess.user.User;
import com.vinkey.restapi.identityandaccess.user.builder.UserBuilder;
import com.vinkey.restapi.identityandaccess.user.builder.UserMother;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Comparator;
import org.hibernate.Hibernate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;

@PersistenceTest
public class TaskRepositoryTest {

  @Autowired private TestEntityManager entityManager;

  @Autowired private TaskHistoryRepository taskRepository;

  private Tenant johnTenant;
  private User john;
  private Group johnGroup;
  private TaskHistory johnTask;
  private ProcessInstanceCustom johnProcessInstance;

  private void createTenants() {
    TenantBuilder tenantBuilder = TenantMother.aSaveableTenant();
    johnTenant = tenantBuilder.but().withUrl("johnBV").build();

    entityManager.persist(johnTenant);
  }

  private void createPeople() {
    IdentityBuilder identityBuilder = IdentityMother.aSaveableIdentity();
    Identity johnIdentity =
        identityBuilder.but().email("<EMAIL>").password("johnDoe1").build();

    entityManager.persist(johnIdentity);

    UserBuilder userBuilder = UserMother.aSaveableUser();
    john =
        userBuilder
            .but()
            .tenant(johnTenant)
            .identity(johnIdentity)
            .firstName("John")
            .lastName("Doe")
            .build();

    john.setCreatedBy(john);
    john.setModifiedBy(john);

    entityManager.persist(john);
  }

  private void createGroups() {
    johnGroup =
        GroupMother.aSaveableGroup().withName("John's group").withTenant(johnTenant).build();

    entityManager.persist(johnGroup);

    johnGroup.getAncestorPaths().stream().forEach(path -> entityManager.persist(path));
  }

  private void createTasks() {

    entityManager.flush();
    entityManager.refresh(john);

    johnProcessInstance = ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build();
    String processInstanceId = johnProcessInstance.getProcessInstanceId();

    TaskCustomBuilder taskCustomBuilder = TaskCustomMother.aSaveableTaskCustom();
    johnTask =
        taskCustomBuilder
            .but()
            .withAssignee(john)
            .withProcessInstance(johnProcessInstance)
            .withProcecessDefinitionKey(johnProcessInstance.getProcessDefinitionKey())
            .withName("John's task")
            .withTenantId(johnTenant.getId().toString())
            .withEndTime(
                Timestamp.from(Instant.now().plusSeconds(60))) // Add 60 seconds to the current time
            .build();

    String taskId = johnTask.getId();

    Variable longProcessVariable =
        VariableMother.aValidLongVariable()
            .withId("longVariable-process-1")
            .withProcessInstance(johnProcessInstance)
            .withTask(null)
            .build();
    Variable textProcessVariable =
        VariableMother.aValidTextVariable()
            .withId("textVariable-process-1")
            .withProcessInstance(johnProcessInstance)
            .withTask(null)
            .build();

    Variable longTaskVariable =
        VariableMother.aValidLongVariable()
            .withId("longVariable-task-1")
            .withProcessInstance(johnProcessInstance)
            .withTask(johnTask)
            .build();
    Variable textTaskVariable =
        VariableMother.aValidTextVariable()
            .withId("textVariable-task-1")
            .withProcessInstance(johnProcessInstance)
            .withTask(johnTask)
            .build();

    IdentityLinkCustom identityLinkCustom =
        IdentityLinkCustomMother.aCandidateIdentityLinkCustomWithARandomGroup()
            .withProcessInstanceId(processInstanceId)
            .withTask(johnTask)
            .build();

    HistoryDetailsCustom historyDetailsCustomWithText =
        HistoryDetailCustomMother.aSaveableTextHistoryDetailCustom()
            .withTask(taskId)
            .withProcessInstanceId(processInstanceId)
            .build();
    HistoryDetailsCustom historyDetailsCustomWithText2 =
        HistoryDetailCustomMother.aSaveableText2HistoryDetailCustom()
            .withTask(taskId)
            .withProcessInstanceId(processInstanceId)
            .build();
    HistoryDetailsCustom historyDetailsCustomWithLong =
        HistoryDetailCustomMother.aSaveableLongHistoryDetailCustom()
            .withTask(taskId)
            .withProcessInstanceId(processInstanceId)
            .build();
    HistoryDetailsCustom historyDetailsCustomWithDouble =
        HistoryDetailCustomMother.aSaveableDoubleHistoryDetailCustom()
            .withTask(taskId)
            .withProcessInstanceId(processInstanceId)
            .build();

    entityManager.persist(johnProcessInstance);
    entityManager.persist(johnTask);
    entityManager.persist(identityLinkCustom);
    entityManager.persist(longProcessVariable);
    entityManager.persist(textProcessVariable);
    entityManager.persist(longTaskVariable);
    entityManager.persist(textTaskVariable);
    entityManager.persist(historyDetailsCustomWithText);
    entityManager.persist(historyDetailsCustomWithText2);
    entityManager.persist(historyDetailsCustomWithLong);
    entityManager.persist(historyDetailsCustomWithDouble);
  }

  @BeforeEach
  void setUp() {
    createTenants();
    createPeople();
    createGroups();
    createTasks();

    entityManager.flush();
    entityManager.clear();
  }

  @Test
  public void findAll_withNoArgument_returnsAPageWithOneTaskAndAllGivenRelationsFetched() {
    Page<TaskHistory> tasks =
        taskRepository.findAll(
            null, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null, null, null, 0L, 10L, null, null);
    TaskHistory task = tasks.getContent().get(0);

    then(tasks).isNotNull();
    then(tasks.getTotalElements()).isEqualTo(1);
    then(task.getName()).isEqualTo("John's task");
    then(task.getTenantId()).isEqualTo(johnTenant.getId().toString());

    then(Hibernate.isInitialized(task.getIdentityLinks())).isTrue();
    then(task.getIdentityLinks().size()).isEqualTo(1);
    then(task.getIdentityLinks().iterator().next().getTask().getId()).isEqualTo(johnTask.getId());
    then(task.getIdentityLinks().iterator().next().getProcessInstanceId())
        .isEqualTo(johnProcessInstance.getProcessInstanceId());

    then(Hibernate.isInitialized(task.getProcessVariables())).isTrue();
    then(task.getProcessVariables().size()).isEqualTo(2);

    Variable longProcessVariable =
        task.getProcessVariables().stream()
            .filter(v -> v.getName().equals("longVariable"))
            .findFirst()
            .get();
    then(longProcessVariable.getId()).isNotNull();
    then(longProcessVariable.getLongValue()).isEqualTo(1L);

    Variable textProcessVariable =
        task.getProcessVariables().stream()
            .filter(v -> v.getName().equals("textVariable"))
            .findFirst()
            .get();
    then(textProcessVariable.getId()).isNotNull();
    then(textProcessVariable.getTextValue()).isEqualTo("textValue");

    then(Hibernate.isInitialized(task.getTaskVariables())).isTrue();
    then(task.getTaskVariables().size()).isEqualTo(2);

    Variable longTaskVariable =
        task.getTaskVariables().stream()
            .filter(v -> v.getName().equals("longVariable"))
            .findFirst()
            .get();
    then(longTaskVariable.getId()).isNotNull();
    then(longTaskVariable.getLongValue()).isEqualTo(1L);

    Variable textTaskVariable =
        task.getTaskVariables().stream()
            .filter(v -> v.getName().equals("textVariable"))
            .findFirst()
            .get();
    then(textTaskVariable.getId()).isNotNull();
    then(textTaskVariable.getTextValue()).isEqualTo("textValue");

    then(Hibernate.isInitialized(task.getHistoryDetails())).isTrue();
    then(task.getHistoryDetails().size()).isEqualTo(4);

    HistoryDetailsCustom historyDetailsCustomWithText =
        task.getHistoryDetails().stream().filter(v -> v.getName().equals("Text")).findFirst().get();
    then(historyDetailsCustomWithText.getId()).isNotNull();
    then(historyDetailsCustomWithText.getText()).isEqualTo("Test text");

    HistoryDetailsCustom historyDetailsCustomWithText2 =
        task.getHistoryDetails().stream()
            .filter(v -> v.getName().equals("Text2"))
            .findFirst()
            .get();
    then(historyDetailsCustomWithText2.getId()).isNotNull();
    then(historyDetailsCustomWithText2.getText2()).isEqualTo("Test text2");

    HistoryDetailsCustom historyDetailsCustomWithLong =
        task.getHistoryDetails().stream().filter(v -> v.getName().equals("Long")).findFirst().get();
    then(historyDetailsCustomWithLong.getId()).isNotNull();
    then(historyDetailsCustomWithLong.getLongValue()).isEqualTo(123L);

    HistoryDetailsCustom historyDetailsCustomWithDouble =
        task.getHistoryDetails().stream()
            .filter(v -> v.getName().equals("Double"))
            .findFirst()
            .get();
    then(historyDetailsCustomWithDouble.getId()).isNotNull();
    then(historyDetailsCustomWithDouble.getDoubleValue()).isEqualTo(123.123);

    then(Hibernate.isInitialized(task.getAssignee())).isTrue();
    then(task.getAssignee().getId()).isEqualTo(john.getId());

    then(task.getCreateTime()).isNotNull();
    then(task.getCreateTime().getTime() - 60000)
        .isLessThan(Timestamp.from(Instant.now()).getTime());

    then(task.getEndTime()).isNotNull();
    then(task.getEndTime().getTime() - 60000).isLessThan(Timestamp.from(Instant.now()).getTime());
  }

  @Test
  public void findAll_withNoArguments_checkIfVariablesRelationIsNullWorkAndGroupIsNull() {

    ProcessInstanceCustom processInstance =
        ProcessInstanceCustomMother.aSaveableProcessInstanceCustom().build();

    TaskHistory extraTask =
        TaskCustomMother.aSaveableTaskCustom()
            .withProcessInstance(processInstance)
            .withTaskVariable(null)
            .withProcessVariable(null)
            .build();

    entityManager.persist(processInstance);
    entityManager.persist(extraTask);
    entityManager.flush();

    Page<TaskHistory> tasks =
        taskRepository.findAll(
            extraTask.getProcessInstanceId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0L,
            10L,
            null,
            null);
    TaskHistory task = tasks.getContent().get(0);

    then(task.getGroup()).isNull();
    then(task.getProcessVariables()).isNull();
    then(task.getTaskVariables()).isNull();
  }

  @Test
  public void findAll_withSortingNull_returnsAPageWithAllTaskSortedOnCreateTime() {

    TaskHistory extraTask = TaskCustomMother.aSaveableTaskCustom().build();

    TaskHistory extraTask2 =
        TaskCustomMother.aSaveableTaskCustom()
            .withCreateTime(Timestamp.from(Instant.now().plusSeconds(3600)))
            .build();

    entityManager.persist(extraTask);
    entityManager.persist(extraTask2);
    entityManager.flush();

    Page<TaskHistory> tasks =
        taskRepository.findAll(
            null, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null, null, null, 0L, 10L, null, null);

    then(tasks).isNotNull();
    then(tasks.getTotalElements()).isEqualTo(3);
    then(tasks.getContent().get(2).getCreateTime().getTime())
        .isLessThan(tasks.getContent().get(1).getCreateTime().getTime());
    then(tasks.getContent().get(1).getCreateTime().getTime())
        .isLessThan(tasks.getContent().get(0).getCreateTime().getTime());
  }

  @Test
  public void findAll_withSortingIDAsc_returnsAPageWithAllTaskSortedOnIdAsc() {

    TaskHistory extraTask = TaskCustomMother.aSaveableTaskCustom().build();

    TaskHistory extraTask2 = TaskCustomMother.aSaveableTaskCustom().build();

    entityManager.persist(extraTask);
    entityManager.persist(extraTask2);
    entityManager.flush();

    Page<TaskHistory> tasks =
        taskRepository.findAll(
            null, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null, "id:asc", null, 0L, 10L, null, null);

    then(tasks).isNotNull();
    then(tasks.getTotalElements()).isEqualTo(3);
    then(tasks.getContent()).isSortedAccordingTo(Comparator.comparing(TaskHistory::getId));
  }

  @Test
  public void findAll_withSortingIDDesc_returnsAPageWithAllTaskSortedOnIdDesc() {

    TaskHistory extraTask = TaskCustomMother.aSaveableTaskCustom().build();

    TaskHistory extraTask2 = TaskCustomMother.aSaveableTaskCustom().build();

    entityManager.persist(extraTask);
    entityManager.persist(extraTask2);
    entityManager.flush();

    Page<TaskHistory> tasks =
        taskRepository.findAll(
            null, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null, "id:desc", null, 0L, 10L, null, null);

    then(tasks).isNotNull();
    then(tasks.getTotalElements()).isEqualTo(3);
    then(tasks.getContent())
        .isSortedAccordingTo(Comparator.comparing(TaskHistory::getId).reversed());
  }

  @Test
  public void findAll_withSortingOnInvalidAttribute_throwsException() {

    TaskHistory extraTask = TaskCustomMother.aSaveableTaskCustom().build();

    TaskHistory extraTask2 = TaskCustomMother.aSaveableTaskCustom().build();

    entityManager.persist(extraTask);
    entityManager.persist(extraTask2);
    entityManager.flush();

    Exception exception =
        org.junit.jupiter.api.Assertions.assertThrows(
            Exception.class,
            () ->
                taskRepository.findAll(
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    "invalidAttribute:asc",
                    null,
                    0L,
                    10L,
                    null,
                    null));

    then(exception.getMessage()).contains("Cannot sort by non sortable value: invalidAttribute");
  }

  @Test
  public void findAll_withJohnTaskAndJohnSubTask_returnsAPageWithJohnTaskAndJohnSubTask() {

    Variable idVariable =
        VariableMother.aIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable sidVariable =
        VariableMother.aSidVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable nameVariable =
        VariableMother.aNameVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withTextValue("Some module text")
            .build();
    Variable groupIdVariable =
        VariableMother.aGroupIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(johnGroup.getId())
            .build();

    entityManager.persist(idVariable);
    entityManager.persist(sidVariable);
    entityManager.persist(nameVariable);
    entityManager.persist(groupIdVariable);

    TaskHistory subTask =
        TaskCustomMother.aSaveableTaskCustom()
            .withProcessInstance(null)
            .withProcecessDefinitionKey(null)
            .withParentTask(johnTask)
            .withTenantId(johnTenant.getId().toString())
            .build();
    entityManager.persist(subTask);
    entityManager.flush();

    Page<TaskHistory> tasks =
        taskRepository.findAll(
            johnTask.getProcessInstanceId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            0L,
            10L,
            null,
            null);

    then(tasks).isNotNull();
    then(tasks.getTotalElements()).isEqualTo(2);
    then(tasks.getContent().get(1).getId()).isEqualTo(johnTask.getId());
    then(tasks.getContent().get(0).getId()).isEqualTo(subTask.getId());
    then(tasks.getContent().get(0).getProcessDefinitionKey())
        .isEqualTo(johnTask.getProcessDefinitionKey());
    then(tasks.getContent().get(0).getProcessInstanceId())
        .isEqualTo(johnTask.getProcessInstanceId());
    then(tasks.getContent().get(0).getParentTaskId()).isEqualTo(johnTask.getId());

    then(tasks.getContent().get(1).getGroup().getId()).isEqualTo(johnGroup.getId());
    then(tasks.getContent().get(1).getProcessInstance().getModuleId()).isEqualTo(1L);
    then(tasks.getContent().get(1).getProcessInstance().getModuleSid()).isEqualTo(1L);
    then(tasks.getContent().get(1).getProcessInstance().getModuleText())
        .isEqualTo("Some module text");
    then(tasks.getContent().get(1).getTenantId()).isEqualTo(johnTenant.getId().toString());

    then(tasks.getContent().get(0).getGroup().getId()).isEqualTo(johnGroup.getId());
    then(tasks.getContent().get(0).getProcessInstance().getModuleId()).isEqualTo(1L);
    then(tasks.getContent().get(0).getProcessInstance().getModuleSid()).isEqualTo(1L);
    then(tasks.getContent().get(0).getProcessInstance().getModuleText())
        .isEqualTo("Some module text");
    then(tasks.getContent().get(0).getTenantId()).isEqualTo(johnTenant.getId().toString());
  }

  @Test
  public void findByIdAndTenantId_withJohnTask_returnJohnTaskWithTransientProperties() {

    Variable idVariable =
        VariableMother.aIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable sidVariable =
        VariableMother.aSidVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable nameVariable =
        VariableMother.aNameVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withTextValue("Some module text")
            .build();
    Variable groupIdVariable =
        VariableMother.aGroupIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(johnGroup.getId())
            .build();

    entityManager.persist(idVariable);
    entityManager.persist(sidVariable);
    entityManager.persist(nameVariable);
    entityManager.persist(groupIdVariable);
    entityManager.flush();
    entityManager.clear();

    TaskHistory task =
        taskRepository.findByIdAndTenantId(johnTask.getId(), johnTenant.getId().toString());

    then(task).isNotNull();
    then(task.getId()).isEqualTo(johnTask.getId());
    then(task.getGroup().getId()).isEqualTo(johnGroup.getId());
    then(task.getProcessDefinitionKey()).isEqualTo(johnTask.getProcessDefinitionKey());
    then(task.getProcessInstanceId()).isEqualTo(johnTask.getProcessInstanceId());
    then(task.getProcessInstance().getModuleId()).isEqualTo(1L);
    then(task.getProcessInstance().getModuleSid()).isEqualTo(1L);
    then(task.getProcessInstance().getModuleText()).isEqualTo("Some module text");
    then(task.getTenantId()).isEqualTo(johnTenant.getId().toString());
  }

  @Test
  public void
      findByIdAndTenantId_withTaskWithoutGroupAndProcessVariables_returnTaskWithOutTransientValues() {
    TaskHistory extraTask =
        TaskCustomMother.aSaveableTaskCustom()
            .withProcessInstance(null)
            .withProcecessDefinitionKey(null)
            .withTenantId(johnTenant.getId().toString())
            .build();
    entityManager.persist(extraTask);

    entityManager.flush();

    TaskHistory task =
        taskRepository.findByIdAndTenantId(extraTask.getId(), johnTenant.getId().toString());

    then(task).isNotNull();
    then(task.getId()).isEqualTo(extraTask.getId());
    then(task.getGroup()).isNull();
    then(task.getProcessInstance()).isNull();
    then(task.getTenantId()).isEqualTo(johnTenant.getId().toString());
  }

  @Test
  public void findByIdAndTenantId_withJohnSubTask_returnTaskWithSubTaskAndTransientValues() {

    Variable idVariable =
        VariableMother.aIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable sidVariable =
        VariableMother.aSidVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(1L)
            .build();
    Variable nameVariable =
        VariableMother.aNameVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withTextValue("Some module text")
            .build();
    Variable groupIdVariable =
        VariableMother.aGroupIdVariableWithNoValue()
            .withProcessInstance(johnProcessInstance)
            .withLongValue(johnGroup.getId())
            .build();

    entityManager.persist(idVariable);
    entityManager.persist(sidVariable);
    entityManager.persist(nameVariable);
    entityManager.persist(groupIdVariable);

    TaskHistory subTask =
        TaskCustomMother.aSaveableTaskCustom()
            .withProcessInstance(null)
            .withProcecessDefinitionKey(null)
            .withParentTask(johnTask)
            .withTenantId(johnTenant.getId().toString())
            .build();
    entityManager.persist(subTask);
    entityManager.flush();

    TaskHistory task =
        taskRepository.findByIdAndTenantId(subTask.getId(), johnTenant.getId().toString());

    then(task).isNotNull();
    then(task.getId()).isEqualTo(subTask.getId());
    then(task.getParentTaskId()).isEqualTo(johnTask.getId());
    then(task.getProcessDefinitionKey()).isEqualTo(johnTask.getProcessDefinitionKey());
    then(task.getProcessInstanceId()).isEqualTo(johnTask.getProcessInstanceId());
    then(task.getGroup().getId()).isEqualTo(johnGroup.getId());
    then(task.getProcessInstance().getModuleId()).isEqualTo(1L);
    then(task.getProcessInstance().getModuleSid()).isEqualTo(1L);
    then(task.getProcessInstance().getModuleText()).isEqualTo("Some module text");
    then(task.getTenantId()).isEqualTo(johnTenant.getId().toString());
  }
}
