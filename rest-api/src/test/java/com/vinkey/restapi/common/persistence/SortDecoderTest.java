package com.vinkey.restapi.common.persistence;

import static org.assertj.core.api.BDDAssertions.then;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import java.util.LinkedList;
import java.util.Queue;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;

public class SortDecoderTest {

  private enum TestSortBy implements SortByEnum {
    NAME("name"),
    AGE("age");

    private final String field;

    TestSortBy(String field) {
      this.field = field;
    }

    @Override
    public String getField() {
      return field;
    }
  }

  @Test
  public void decode_withSortStringWithTwoSortings_returnQueueWithTwoValidOrderObjects() {
    String sortString = "name:asc,age:desc";
    Queue<Order> expectedOrders = new LinkedList<>();
    expectedOrders.add(new Order(Direction.ASC, "name"));
    expectedOrders.add(new Order(Direction.DESC, "age"));

    Queue<Order> actualOrders = SortDecoder.decode(sortString, TestSortBy.class);

    then(actualOrders).isEqualTo(expectedOrders);
  }

  @Test
  public void decode_withSortStringThatDoesntMatchPattern_returnsQueueWithOnlyTwoOrderObjects() {
    String sortString = "name:asc,age:desc,invalid";

    Queue<Order> expectedOrder = new LinkedList<>();
    expectedOrder.add(new Order(Direction.ASC, "name"));
    expectedOrder.add(new Order(Direction.DESC, "age"));

    Queue<Order> actualOrders = SortDecoder.decode(sortString, TestSortBy.class);

    then(actualOrders.size()).isEqualTo(2);
    then(actualOrders).isEqualTo(expectedOrder);
  }

  @Test
  public void decode_withEmptySortString_returnsNull() {
    String sortString = "";

    Queue<Order> actualOrders = SortDecoder.decode(sortString, TestSortBy.class);

    then(actualOrders).isNull();
  }

  @Test
  public void decode_withNullSortString_returnsNull() {
    String sortString = null;

    Queue<Order> actualOrders = SortDecoder.decode(sortString, TestSortBy.class);

    then(actualOrders).isNull();
  }

  @Test
  public void decode_withOnlyInvalidInSortString_returnsNull() {
    String sortString = "asc(name),desc(age)";

    Queue<Order> actualOrders = SortDecoder.decode(sortString, TestSortBy.class);

    then(actualOrders).isNull();
  }
}
