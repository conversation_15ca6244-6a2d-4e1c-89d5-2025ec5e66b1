package com.vinkey.restapi.location.autodesk;

import com.vinkey.restapi.location.autodesk.dto.SignedUploadFinalizeRead;
import com.vinkey.restapi.location.autodesk.dto.SignedUploadUrlRead;
import com.vinkey.restapi.location.autodesk.dto.TokenRead;
import java.net.URI;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

@Service
public class AutoDeskClient {
  private static final String APS_BASE_URL = "https://developer.api.autodesk.com";

  private final String clientId;
  private final String clientSecret;
  private final String bucketKey;

  private final String fullScope =
      "code:all data:write data:read bucket:create bucket:delete bucket:read";
  private final String readOnlyScope = "viewables:read";

  private String accessToken;
  private long accessTokenExpiresAt = 0L; // epoch millis

  private final RestTemplate client;

  public AutoDeskClient(
      RestTemplateBuilder restTemplateBuilder,
      @Value("${autodesk.client.id}") String clientId,
      @Value("${autodesk.client.secret}") String clientSecret,
      @Value("${autodesk.client.bucketKey}") String bucketKey)
      throws Exception {
    this.client = restTemplateBuilder.build();
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.bucketKey = bucketKey;

    this.accessToken = getAccessToken(this.fullScope);
  }

  private String getAccessToken(String scope) throws Exception {
    String authenticationToken =
        Base64.getEncoder().encodeToString((this.clientId + ":" + this.clientSecret).getBytes());

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    headers.set("Authorization", "Basic " + authenticationToken);

    MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("grant_type", "client_credentials");
    requestBody.add("scope", scope);

    HttpEntity<MultiValueMap<String, String>> requestEntity =
        new HttpEntity<>(requestBody, headers);

    ResponseEntity<TokenRead> responseEntity =
        client.exchange(
            APS_BASE_URL + "/authentication/v2/token",
            HttpMethod.POST,
            requestEntity,
            TokenRead.class);

    if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
      TokenRead tokenResponse = responseEntity.getBody();

      // assume TokenRead has an 'expires_in' field in seconds
      long expiresInSeconds = tokenResponse.expires_in;
      this.accessTokenExpiresAt = System.currentTimeMillis() + (expiresInSeconds * 1_000L);

      return tokenResponse.access_token;
    }

    throw new RuntimeException("Failed to retrieve access token");
  }

  /** Make sure we have a valid token before each call. */
  private synchronized void ensureAccessTokenValid() {
    if (System.currentTimeMillis() >= this.accessTokenExpiresAt) {
      try {
        this.accessToken = getAccessToken(this.fullScope);
      } catch (Exception e) {
        throw new RuntimeException("Failed to refresh access token", e);
      }
    }
  }

  public void createBucket(String ossBucketKey) {
    ensureAccessTokenValid();

    String BUCKETS_URL = APS_BASE_URL + "/oss/v2/buckets";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("Authorization", "Bearer " + accessToken);

    MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("bucketKey", ossBucketKey);
    requestBody.add("access", "full");
    requestBody.add("policyKey", "transient");

    HttpEntity<MultiValueMap<String, Object>> requestEntity =
        new HttpEntity<>(requestBody, headers);

    ResponseEntity<Void> responseEntity =
        client.exchange(BUCKETS_URL, HttpMethod.POST, requestEntity, Void.class);

    if (responseEntity.getStatusCode() != HttpStatus.CREATED) {
      throw new RuntimeException("Failed to create bucket");
    }
  }

  public SignedUploadUrlRead getSignedS3UploadURL(
      String bucketKey, String objectKey, int lifespanInMinutes) {
    ensureAccessTokenValid();

    String SIGNED_UPLOAD_URL_TEMPLATE =
        APS_BASE_URL + "/oss/v2/buckets/%s/objects/%s/signeds3upload?minutesExpiration=%d";

    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer " + accessToken);
    headers.setContentType(MediaType.APPLICATION_JSON);

    // body params are ignored on GET, but kept for compatibility
    MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("ossbucketKey", bucketKey);
    requestBody.add("ossSourceFileObjectKey", objectKey);
    requestBody.add("access", "full");
    requestBody.add("policyKey", "transient");

    HttpEntity<MultiValueMap<String, String>> requestEntity =
        new HttpEntity<>(requestBody, headers);

    String url = String.format(SIGNED_UPLOAD_URL_TEMPLATE, bucketKey, objectKey, lifespanInMinutes);
    ResponseEntity<SignedUploadUrlRead> responseEntity =
        client.exchange(url, HttpMethod.GET, requestEntity, SignedUploadUrlRead.class);

    if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
      return responseEntity.getBody();
    }

    throw new RuntimeException("Failed to get signed S3 upload URL");
  }

  public String uploadFile(MultipartFile multipartFile, String fileName) throws Exception {
    ensureAccessTokenValid();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

    SignedUploadUrlRead urlResponse = getSignedS3UploadURL(bucketKey, fileName, 5);
    URI decodedUrl = URI.create(urlResponse.urls.get(0));

    RequestCallback requestCallback =
        request -> {
          request.getHeaders().addAll(headers);
          request.getBody().write(multipartFile.getBytes());
        };
    ResponseExtractor<ResponseEntity<Void>> responseExtractor =
        client.responseEntityExtractor(Void.class);
    ResponseEntity<Void> responseEntity =
        client.execute(decodedUrl, HttpMethod.PUT, requestCallback, responseExtractor);

    String urn = finalizeSignedS3Upload(fileName, urlResponse.uploadKey);
    String base64EncodedUrn =
        Base64.getUrlEncoder().withoutPadding().encodeToString(urn.getBytes());

    submitModelDerivativeJob(base64EncodedUrn, fileName);

    if (responseEntity.getStatusCode() != HttpStatus.OK) {
      throw new RuntimeException("Failed to upload file");
    }

    return base64EncodedUrn;
  }

  public String finalizeSignedS3Upload(String objectKey, String uploadKey) {
    ensureAccessTokenValid();

    String SIGNED_UPLOAD_KEY_URL_TEMPLATE =
        APS_BASE_URL + "/oss/v2/buckets/%s/objects/%s/signeds3upload";

    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer " + accessToken);
    headers.setContentType(MediaType.APPLICATION_JSON);

    Map<String, String> requestBody = new HashMap<>();
    requestBody.put("ossbucketKey", bucketKey);
    requestBody.put("ossSourceFileObjectKey", objectKey);
    requestBody.put("access", "full");
    requestBody.put("uploadKey", uploadKey);

    HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

    String url = String.format(SIGNED_UPLOAD_KEY_URL_TEMPLATE, bucketKey, objectKey);
    ResponseEntity<SignedUploadFinalizeRead> responseEntity =
        client.exchange(url, HttpMethod.POST, requestEntity, SignedUploadFinalizeRead.class);

    if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
      return responseEntity.getBody().objectId;
    }

    throw new RuntimeException("Failed to get signed S3 upload key");
  }

  public void submitModelDerivativeJob(String base64EncodedUrn, String fileName) {
    ensureAccessTokenValid();

    String JOB_SUBMIT_URL = APS_BASE_URL + "/modelderivative/v2/designdata/job";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("Authorization", "Bearer " + accessToken);
    headers.set("x-ads-force", "true");

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("input", getInputParams(base64EncodedUrn));
    requestBody.put("output", getOutputParams());

    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

    ResponseEntity<Void> responseEntity =
        client.exchange(JOB_SUBMIT_URL, HttpMethod.POST, requestEntity, Void.class);

    if (responseEntity.getStatusCode() != HttpStatus.OK) {
      throw new RuntimeException("Failed to submit model derivative job");
    }
  }

  private Map<String, Object> getInputParams(String base64EncodedUrn) {
    Map<String, Object> inputParams = new HashMap<>();
    inputParams.put("urn", base64EncodedUrn);
    return inputParams;
  }

  private Map<String, Object> getOutputParams() {
    Map<String, Object> outputParams = new HashMap<>();
    Map<String, String> destination = new HashMap<>();
    destination.put("region", "EMEA");
    outputParams.put("destination", destination);
    Map<String, Object> format = new HashMap<>();
    format.put("type", "svf2");
    format.put("views", List.of("2d", "3d"));
    outputParams.put("formats", List.of(format));
    return outputParams;
  }

  /** Always fetch a fresh read-only token. */
  public String getReadOnlyAccesToken() {
    try {
      return getAccessToken(readOnlyScope);
    } catch (Exception e) {
      throw new RuntimeException("Failed to get read only access token", e);
    }
  }
}
