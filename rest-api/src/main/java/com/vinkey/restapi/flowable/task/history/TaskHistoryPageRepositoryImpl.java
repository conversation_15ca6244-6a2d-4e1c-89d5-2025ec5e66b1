package com.vinkey.restapi.flowable.task.history;

import com.vinkey.restapi.common.persistence.PagableRepository;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import com.vinkey.restapi.common.recurrence.Recurrence_;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom_;
import com.vinkey.restapi.flowable.task.TaskSortBy;
import com.vinkey.restapi.flowable.task.dto.TaskPriority;
import com.vinkey.restapi.flowable.task.label.TaskLabel_;
import com.vinkey.restapi.flowable.task.runtime.TaskRuntime;
import com.vinkey.restapi.flowable.variable.Variable;
import com.vinkey.restapi.flowable.variable.VariableRepository;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.group.GroupRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.EntityGraph;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.transaction.annotation.Transactional;

public class TaskHistoryPageRepositoryImpl extends PagableRepository<TaskHistory>
    implements TaskHistoryPageRepository {

  private final GroupRepository groupRepository;
  private final VariableRepository variableRepository;

  public TaskHistoryPageRepositoryImpl(
      GroupRepository groupRepository, VariableRepository variableRepository) {
    this.groupRepository = groupRepository;
    this.variableRepository = variableRepository;
  }

  // region Collect and set transient properties

  private Long retrieveGroupIdFromVariables(Set<Variable> variables) {
    if (variables == null) {
      return null;
    }

    Variable groupId =
        variables.stream().filter(v -> v.getName().equals("groupId")).findFirst().orElse(null);
    return groupId != null ? groupId.getLongValue() : null;
  }

  private Long retrieveCurrentGroupIdFromTask(TaskHistory task) {
    Long localGroupId = retrieveGroupIdFromVariables(task.getTaskVariables());

    Set<Variable> processVariables = new HashSet<>();

    if (task.getProcessVariables() != null) {
      processVariables.addAll(task.getProcessVariables());
    }

    if (task.getParentProcessVariables() != null) {
      processVariables.addAll(task.getParentProcessVariables());
    }

    Long globalGroupId = retrieveGroupIdFromVariables(processVariables);

    Long sgroupId = localGroupId == null ? globalGroupId : localGroupId;
    return sgroupId;
  }

  private void setTransientProperties(
      ProcessInstanceCustom processInstanceCustom,
      TaskHistory task,
      Group group,
      List<Variable> processVariables) {
    if (processVariables == null) {
      processVariables = new ArrayList<>();
    }

    Variable moduleId =
        processVariables.stream().filter(v -> v.getName().equals("id")).findFirst().orElse(null);
    Variable moduleSid =
        processVariables.stream().filter(v -> v.getName().equals("sid")).findFirst().orElse(null);
    Variable moduleName =
        processVariables.stream().filter(v -> v.getName().equals("name")).findFirst().orElse(null);
    task.setGroup(group);

    if (processInstanceCustom != null) {
      processInstanceCustom.setModuleId(moduleId != null ? moduleId.getLongValue() : null);
      processInstanceCustom.setModuleSid(moduleSid != null ? moduleSid.getLongValue() : null);
      processInstanceCustom.setModuleText(moduleName != null ? moduleName.getTextValue() : null);
    }
  }

  // endregion

  // region Extra process related queries

  // ? Return map structure is: {parenTaskId: '...', processInstance: '...'}
  private Map<String, ProcessInstanceCustom> getProcessInstanceFromParentTaskId(
      Set<String> parentTaskIds) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<Tuple> criteriaQuery = criteriaBuilder.createTupleQuery();

    Root<TaskHistory> root = criteriaQuery.from(TaskHistory.class);

    Join<TaskHistory, ProcessInstanceCustom> joinProcess =
        root.join(TaskHistory_.processInstance, JoinType.LEFT);

    criteriaQuery.multiselect(
        root.get(TaskHistory_.ID).alias("parentTaskId"), joinProcess.alias("processInstance"));
    criteriaQuery.where(criteriaBuilder.in(root.get(TaskHistory_.ID)).value(parentTaskIds));

    Map<String, ProcessInstanceCustom> parentTaskProcessInstanceIds = new HashMap<>();

    for (Tuple tuple : entityManager.createQuery(criteriaQuery).getResultList()) {
      parentTaskProcessInstanceIds.put(
          tuple.get("parentTaskId", String.class),
          tuple.get("processInstance", ProcessInstanceCustom.class));
    }

    return parentTaskProcessInstanceIds;
  }

  private ProcessInstanceCustom getProcessInstanceFromParentTaskId(String parentTaskId) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<ProcessInstanceCustom> criteriaQuery =
        criteriaBuilder.createQuery(ProcessInstanceCustom.class);
    Root<ProcessInstanceCustom> root = criteriaQuery.from(ProcessInstanceCustom.class);

    Subquery<String> subquery = criteriaQuery.subquery(String.class);
    Root<TaskHistory> subRoot = subquery.from(TaskHistory.class);
    subquery.select(subRoot.get(TaskHistory_.PROCESS_INSTANCE).get(ProcessInstanceCustom_.ID));
    subquery.where(criteriaBuilder.equal(subRoot.get(TaskHistory_.ID), parentTaskId));

    criteriaQuery.where(criteriaBuilder.in(root.get(ProcessInstanceCustom_.ID)).value(subquery));

    TypedQuery<ProcessInstanceCustom> typedQuery = entityManager.createQuery(criteriaQuery);

    return typedQuery.getSingleResult();
  }

  // endregion

  private Page<TaskHistory> getPage(
      Root<TaskHistory> root,
      CriteriaQuery<TaskHistory> query,
      Pageable pageable,
      Specification<TaskHistory> spec,
      Map<String, Object> hints) {
    Page<TaskHistory> page =
        super.getPage(query, root, TaskHistory.class, pageable, spec, TaskHistory_.ID, hints);

    Set<String> getParentTaskIds =
        page.getContent().stream().map(TaskHistory::getParentTaskId).collect(Collectors.toSet());

    Map<String, ProcessInstanceCustom> subTaskParentTaskIdProcessInstanceMap =
        getProcessInstanceFromParentTaskId(getParentTaskIds);

    Set<String> subTasksProcessInstanceIdSet =
        subTaskParentTaskIdProcessInstanceMap.values().stream()
            .map(ProcessInstanceCustom::getProcessInstanceId)
            .collect(Collectors.toSet());

    Set<Variable> subTasksVariables =
        variableRepository.findAllByTaskIdInAndNameIsIdOrNameIsSidOrNameIsNameOrNameIsGroupId(
            subTasksProcessInstanceIdSet);

    Map<String, List<Variable>> subTasksVariablesMap =
        subTasksVariables.stream().collect(Collectors.groupingBy(Variable::getProcessInstanceId));

    List<TaskHistory> content =
        page.getContent().stream()
            .map(
                t -> {
                  if (subTaskParentTaskIdProcessInstanceMap.containsKey(t.getParentTaskId())) {
                    t.setProcessInstance(
                        subTaskParentTaskIdProcessInstanceMap.get(t.getParentTaskId()));
                    t.setProcessDefinitionKey(t.getProcessInstance().getProcessDefinitionKey());

                    Set<Variable> parentTaskProcessVariables =
                        subTasksVariablesMap.get(t.getProcessInstanceId()) != null
                            ? subTasksVariablesMap.get(t.getProcessInstanceId()).stream()
                                .collect(Collectors.toSet())
                            : new HashSet<>();
                    if (t.getProcessVariables() != null) {
                      parentTaskProcessVariables.addAll(t.getProcessVariables());
                    }
                    t.setParentProcessVariables(parentTaskProcessVariables);
                  }
                  return t;
                })
            .toList();

    Set<Long> groupIds =
        content.stream().map(this::retrieveCurrentGroupIdFromTask).collect(Collectors.toSet());

    Set<Group> groups =
        groupRepository.findAllByIdIn(groupIds.stream().collect(Collectors.toList()));

    List<TaskHistory> pageUpdated =
        content.stream()
            .map(
                (t) -> {
                  Long sgroupId = retrieveCurrentGroupIdFromTask(t);
                  Group group =
                      groups.stream()
                          .filter(g -> g.getId().equals(sgroupId))
                          .findFirst()
                          .orElse(null);
                  Set<Variable> processVariables = new HashSet<>();
                  if (t.getProcessVariables() != null) {
                    processVariables.addAll(t.getProcessVariables());
                  }

                  if (t.getParentProcessVariables() != null) {
                    processVariables.addAll(t.getParentProcessVariables());
                  }

                  setTransientProperties(
                      t.getProcessInstance(), t, group, processVariables.stream().toList());
                  return t;
                })
            .toList();
    return new PageImpl<>(pageUpdated, page.getPageable(), page.getTotalElements());
  }

  @Override
  public Page<TaskHistory> findAll(
      String processInstanceId,
      List<String> processInstanceIds,
      List<String> candidateGroups,
      List<Long> candidateUsers,
      String groupId,
      String ancestorGroupId,
      String taskDefinitionKey,
      String taskDefinitionKeyNot,
      List<String> processDefinitionKeys,
      Long owner,
      Long assignee,
      Long excludeUser,
      Set<Long> labels,
      Long startDate,
      Long dueDate,
      Long startDateGte,
      Long dueDateGte,
      Long startDateLte,
      Long dueDateLte,
      String search,
      String filter,
      String sort,
      Long tenantId,
      Long pageNumber,
      Long pageSize,
      TaskHistoryStatus status,
      TaskPriority priority) {

    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

    CriteriaQuery<TaskHistory> criteriaQuery = criteriaBuilder.createQuery(TaskHistory.class);
    criteriaQuery.distinct(true);

    Root<TaskHistory> root = criteriaQuery.from(TaskHistory.class);

    EntityGraph<TaskHistory> entityGraph = entityManager.createEntityGraph(TaskHistory.class);
    entityGraph.addAttributeNodes(TaskHistory_.IDENTITY_LINKS);
    entityGraph.addAttributeNodes(TaskHistory_.TASK_VARIABLES);
    entityGraph.addAttributeNodes(TaskHistory_.PROCESS_VARIABLES);
    entityGraph.addAttributeNodes(TaskHistory_.HISTORY_DETAILS);
    entityGraph.addAttributeNodes(TaskHistory_.ASSIGNEE);
    entityGraph.addAttributeNodes(TaskHistory_.OWNER);
    entityGraph.addAttributeNodes(TaskHistory_.PARENT_TASK);
    entityGraph.addAttributeNodes(TaskHistory_.PROCESS_INSTANCE);
    entityGraph.addSubgraph(TaskHistory_.RECURRENCE).addAttributeNodes(Recurrence_.DAYS_OF_WEEK);
    entityGraph.addSubgraph(TaskHistory_.LABELS).addAttributeNodes(TaskLabel_.LABEL);
    entityGraph.addSubgraph(TaskHistory_.FILES);

    Map<String, Object> hints = new HashMap<>();
    hints.put("javax.persistence.fetchgraph", entityGraph);

    TaskHistorySpecificationBuilder taskSpecificationBuilder =
        new TaskHistorySpecificationBuilder()
            .search(search)
            .ancestorGroupId(ancestorGroupId)
            .taskDefinitionKey(taskDefinitionKey)
            .taskDefinitionKeyNot(taskDefinitionKeyNot)
            .processDefinitionKeys(processDefinitionKeys)
            .owner(owner)
            .assignee(assignee)
            .groupId(groupId)
            .candidateGroups(candidateGroups)
            .candidateUsers(candidateUsers)
            .excludeUser(excludeUser)
            .labels(labels)
            .processInstanceId(processInstanceId)
            .processInstanceIds(processInstanceIds)
            .tenantId(tenantId)
            .filter(filter)
            .status(status)
            .priority(priority)
            .startDate(startDate)
            .dueDate(dueDate)
            .startDateGte(startDateGte)
            .startDateLte(startDateLte)
            .dueDateGte(dueDateGte)
            .dueDateLte(dueDateLte);

    Specification<TaskHistory> spec = taskSpecificationBuilder.build();

    if (spec != null) {
      Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);
      criteriaQuery.where(predicate);
    }

    List<Order> sortByList =
        sort != null
            ? SortDecoder.decode(sort, TaskSortBy.class).stream().toList()
            : new ArrayList<>();

    Sort sortBy = Sort.by(Order.desc(TaskHistory_.CREATE_TIME));

    if (!sortByList.isEmpty()) {
      sortBy = Sort.by(sortByList);
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    return getPage(root, criteriaQuery, pageable, spec, hints);
  }

  @Override
  public TaskHistory findByIdAndTenantId(String id, String tenantId) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();

    CriteriaQuery<TaskHistory> criteriaQuery = criteriaBuilder.createQuery(TaskHistory.class);
    criteriaQuery.distinct(true);

    Root<TaskHistory> root = criteriaQuery.from(TaskHistory.class);

    EntityGraph<TaskHistory> entityGraph = entityManager.createEntityGraph(TaskHistory.class);
    entityGraph.addAttributeNodes(TaskHistory_.IDENTITY_LINKS);
    entityGraph.addAttributeNodes(TaskHistory_.TASK_VARIABLES);
    entityGraph.addAttributeNodes(TaskHistory_.PROCESS_VARIABLES);
    entityGraph.addAttributeNodes(TaskHistory_.HISTORY_DETAILS);
    entityGraph.addAttributeNodes(TaskHistory_.ASSIGNEE);
    entityGraph.addAttributeNodes(TaskHistory_.OWNER);
    entityGraph.addAttributeNodes(TaskHistory_.PARENT_TASK);
    entityGraph.addAttributeNodes(TaskHistory_.PROCESS_INSTANCE);
    entityGraph.addSubgraph(TaskHistory_.RECURRENCE).addAttributeNodes(Recurrence_.DAYS_OF_WEEK);
    entityGraph.addSubgraph(TaskHistory_.LABELS).addAttributeNodes(TaskLabel_.LABEL);
    entityGraph.addSubgraph(TaskHistory_.FILES);

    Map<String, Object> hints = new HashMap<>();
    hints.put("javax.persistence.fetchgraph", entityGraph);

    TaskHistorySpecificationBuilder taskSpecificationBuilder =
        new TaskHistorySpecificationBuilder().tenantId(Long.valueOf(tenantId));

    Specification<TaskHistory> spec = taskSpecificationBuilder.build();

    if (spec != null) {
      Predicate predicate = spec.toPredicate(root, criteriaQuery, criteriaBuilder);
      criteriaQuery.where(
          criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(TaskHistory_.ID), id)));
    }

    criteriaQuery.orderBy(criteriaBuilder.desc(root.get(TaskHistory_.CREATE_TIME)));

    TypedQuery<TaskHistory> typedQuery = entityManager.createQuery(criteriaQuery);
    for (Map.Entry<String, Object> hint : hints.entrySet()) {
      typedQuery.setHint(hint.getKey(), hint.getValue());
    }

    List<TaskHistory> tasks = typedQuery.getResultList();

    if (tasks == null || tasks.isEmpty()) {
      return null;
    }

    TaskHistory task = tasks.get(0);

    // ? Sub tasks do not have processInstanceId, but they have parentTaskId
    if (task.getProcessInstanceId() == null && task.getParentTaskId() != null) {
      ProcessInstanceCustom processInstanceCustom =
          getProcessInstanceFromParentTaskId(task.getParentTaskId());
      task.setProcessInstance(processInstanceCustom);
      task.setProcessDefinitionKey(processInstanceCustom.getProcessDefinitionKey());

      Set<Variable> processVariables =
          variableRepository.findAllByTaskIdInAndNameIsIdOrNameIsSidOrNameIsNameOrNameIsGroupId(
              processInstanceCustom.getProcessInstanceId());
      task.setParentProcessVariables(processVariables);
    }

    if (task.getProcessInstanceId() != null) {
      task.setProcessDefinitionKey(task.getProcessInstance().getProcessDefinitionKey());
    }

    Long sgroupId = retrieveCurrentGroupIdFromTask(task);
    Group group = null;

    if (sgroupId != null) {
      group = groupRepository.findById(sgroupId).orElse(null);
    }

    if (task.getProcessVariables() == null) {
      task.setProcessVariables(new HashSet<>());
    }

    Set<Variable> processVariables = new HashSet<>();
    if (task.getProcessVariables() != null) {
      processVariables.addAll(task.getProcessVariables());
    }

    if (task.getParentProcessVariables() != null) {
      processVariables.addAll(task.getParentProcessVariables());
    }

    setTransientProperties(
        task.getProcessInstance(), task, group, processVariables.stream().toList());

    return task;
  }

  @Transactional
  @Override
  public TaskHistory saveTask(TaskHistory task) {

    JpaEntityInformation<TaskHistory, ?> taskHistoryInformation =
        JpaEntityInformationSupport.getEntityInformation(TaskHistory.class, entityManager);

    if (taskHistoryInformation.isNew(task)) {
      entityManager.persist(task);
    } else {
      task = entityManager.merge(task);
    }

    if (task.getEndTime() == null) {
      TaskRuntime taskRuntimeCustom = new TaskRuntime();
      taskRuntimeCustom.setId(task.getId());
      taskRuntimeCustom.setName(task.getName());
      taskRuntimeCustom.setAssignee(task.getAssignee());
      taskRuntimeCustom.setProcessInstance(task.getProcessInstance());
      taskRuntimeCustom.setProcessDefinitionKey(task.getProcessDefinitionKey());
      taskRuntimeCustom.setExecutionId(task.getExecutionId());
      taskRuntimeCustom.setFormKey(task.getFormKey());
      taskRuntimeCustom.setTaskDefinitionKey(task.getTaskDefinitionKey());
      taskRuntimeCustom.setDescription(task.getDescription());
      taskRuntimeCustom.setParentTask(task.getParentTask());
      taskRuntimeCustom.setTenantId(task.getTenantId());
      taskRuntimeCustom.setStartTime(task.getCreateTime());
      taskRuntimeCustom.setOwner(task.getOwner());
      taskRuntimeCustom.setDelegation(task.getDelegation());
      taskRuntimeCustom.setPriority(task.getPriority());
      taskRuntimeCustom.setStartDate(task.getStartDate());
      taskRuntimeCustom.setDueDate(task.getDueDate());
      taskRuntimeCustom.setRecurrence(task.getRecurrence());

      JpaEntityInformation<TaskRuntime, ?> taskRuntimeInformation =
          JpaEntityInformationSupport.getEntityInformation(TaskRuntime.class, entityManager);

      if (taskRuntimeInformation.isNew(taskRuntimeCustom)) {
        entityManager.persist(taskRuntimeCustom);
      } else {
        taskRuntimeCustom = entityManager.merge(taskRuntimeCustom);
      }
    } else {
      TaskRuntime taskRuntimeCustom = entityManager.find(TaskRuntime.class, task.getId());
      if (taskRuntimeCustom != null) {
        entityManager.remove(taskRuntimeCustom);
      }
    }

    return task;
  }
}
