package com.vinkey.restapi.flowable.task;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;
import com.vinkey.restapi.flowable.task.history.TaskHistory_;

public enum TaskSortBy implements SortByEnum {
  ID(TaskHistory_.ID),
  NAME(TaskHistory_.NAME),
  CREATE_TIME(TaskHistory_.CREATE_TIME),
  LAST_UPDATED_TIME(TaskHistory_.LAST_UPDATED_TIME),
  START_DATE(TaskHistory_.START_DATE),
  DUE_DATE(TaskHistory_.DUE_DATE),
  PRIORITY(TaskHistory_.PRIORITY);

  private final String field;

  TaskSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
