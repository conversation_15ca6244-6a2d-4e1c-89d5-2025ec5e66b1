package com.vinkey.restapi.stats;

import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.FilterSpecificationBuilder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.deviation.DeviationSpecificationBuilder;
import com.vinkey.restapi.communication.instruction.InstructionSpecificationBuilder;
import com.vinkey.restapi.communication.shiftreport.ShiftReportSpecificationBuilder;
import com.vinkey.restapi.communication.shiftreport.log.ShiftReportLogSpecificationBuilder;
import com.vinkey.restapi.compliance.externalaudit.ExternalAuditSpecificationBuilder;
import com.vinkey.restapi.compliance.finding.FindingSpecificationBuilder;
import com.vinkey.restapi.compliance.internalaudit.InternalAuditSpecificationBuilder;
import com.vinkey.restapi.flowable.task.history.TaskHistorySpecificationBuilder;
import com.vinkey.restapi.healthandsafety.incident.IncidentSpecificationBuilder;
import com.vinkey.restapi.healthandsafety.observation.ObservationScoreSpecificationBuilder;
import com.vinkey.restapi.healthandsafety.safetywalk.SafetyWalkSpecificationBuilder;
import com.vinkey.restapi.managementofchange.change.ChangeSpecificationBuilder;
import com.vinkey.restapi.managementofchange.checklist.ChecklistSpecificationBuilder;
import com.vinkey.restapi.managementofchange.checklist.answer.ChecklistAnswerSpecificationBuilder;
import com.vinkey.restapi.permittowork.lototo.LototoSpecificationBuilder;
import com.vinkey.restapi.permittowork.lototo.item.LototoItemSpecificationBuilder;
import com.vinkey.restapi.permittowork.tra.TraSpecificationBuilder;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitSpecificationBuilder;
import com.vinkey.restapi.stats.dto.GroupByEnum;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@Service
public class StatisticsService {

  private StatisticsRepository statisticsRepository;

  public StatisticsService(StatisticsRepository statisticsRepository) {
    this.statisticsRepository = statisticsRepository;
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      or @membershipService.isMember(#ancestorGroupId, authentication.principal.userId)
      or @membershipService.isMember(#groupId, authentication.principal.userId)
      or @taskService.checkProcessReadPermission(#processInstanceId, authentication.principal.userId)
  """)
  public Map<Object, Object> getStats(
      Long groupId,
      Long ancestorGroupId,
      String timeZone,
      String processInstanceId,
      Long startDate,
      Long endDate,
      TimeSerieModules module,
      String filter,
      List<GroupByEnum> groupBy,
      Long tenantId) {
    FilterSpecificationBuilder specBuilder = null;
    switch (module) {
      case SAFETYWALKS:
        specBuilder =
            new SafetyWalkSpecificationBuilder()
                .ancestorGroupId(ancestorGroupId)
                .groupId(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenantId(tenantId);
        break;
      case OBSERVATIONS:
        specBuilder =
            new ObservationScoreSpecificationBuilder()
                .ancestorIdOrAssignedGroupId(ancestorGroupId)
                .groupId(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .withObservation(true)
                .tenantId(tenantId);
        break;
      case INCIDENTS:
        specBuilder =
            new IncidentSpecificationBuilder()
                .groupId(groupId)
                .ancestorGroupId(ancestorGroupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenantId(tenantId);
        break;
      case WORKPERMITS:
        specBuilder =
            new WorkPermitSpecificationBuilder()
                .groupId(groupId)
                .ancestorGroupIdOrlocationOwnerAncestor(ancestorGroupId)
                .creationDateGte(startDate)
                .creationDateLte(endDate)
                .tenantId(tenantId);
        break;
      case TRAS:
        specBuilder =
            new TraSpecificationBuilder()
                .ancestorGroupId(ancestorGroupId)
                .groupId(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenantId(tenantId);
        break;
      case LOTOTO_PLANS:
        specBuilder =
            new LototoSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .plannedDateGte(startDate)
                .plannedDateLte(endDate)
                .tenant(tenantId);
        break;
      case LOTOTO_ITEMS:
        specBuilder =
            new LototoItemSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .plannedDateGte(startDate)
                .plannedDateLte(endDate)
                .tenant(tenantId);
        break;
      case CHANGES:
        specBuilder =
            new ChangeSpecificationBuilder()
                .ancestorId(ancestorGroupId)
                .group(groupId)
                .startDateGte(startDate)
                .startDateLte(endDate)
                .tenant(tenantId);
        break;
      case CHECKLISTS:
        specBuilder =
            new ChecklistSpecificationBuilder()
                .ancestorId(ancestorGroupId)
                .group(groupId)
                .startDateGte(startDate)
                .startDateLte(endDate)
                .tenant(tenantId);
        break;
      case CHECKLIST_ANSWERS:
        specBuilder =
            new ChecklistAnswerSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .startDateGte(startDate)
                .startDateLte(endDate)
                .tenant(tenantId);
        break;
      case INSTRUCTIONS:
        specBuilder =
            new InstructionSpecificationBuilder()
                .pathGroup(ancestorGroupId)
                .group(groupId)
                .startDateGte(startDate)
                .startDateLte(endDate)
                .tenant(tenantId);
        break;
      case DEVIATIONS:
        specBuilder =
            new DeviationSpecificationBuilder()
                .pathGroupId(ancestorGroupId)
                .groupId(groupId)
                .tenantId(tenantId);
        break;
      case SHIFT_REPORTS:
        specBuilder =
            new ShiftReportSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .tenant(tenantId);
        break;
      case SHIFT_REPORT_LOGS:
        specBuilder =
            new ShiftReportLogSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .tenant(tenantId);
        break;
      case TASKS:
        specBuilder =
            new TaskHistorySpecificationBuilder()
                .ancestorGroupId(ancestorGroupId != null ? ancestorGroupId.toString() : null)
                .groupId(groupId != null ? groupId.toString() : null)
                .tenantId(tenantId)
                .processInstanceId(processInstanceId)
                .deleteReasonNull();
        break;
      case INTERNAL_AUDITS:
        specBuilder =
            new InternalAuditSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenantId(tenantId);
        break;
      case EXTERNAL_AUDITS:
        specBuilder =
            new ExternalAuditSpecificationBuilder()
                .ancestorGroup(ancestorGroupId)
                .group(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenantId(tenantId);
        break;
      case FINDINGS:
        specBuilder =
            new FindingSpecificationBuilder()
                .ancestorId(ancestorGroupId)
                .group(groupId)
                .dateGte(startDate)
                .dateLte(endDate)
                .tenant(tenantId);
        break;
      default:
        break;
    }

    if (filter != null && specBuilder != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        specBuilder.withFilter(filterSpecs.poll());
      }
    }

    return statisticsRepository.getStats(specBuilder.build(), module, groupBy, timeZone);
  }
}
