package com.vinkey.restapi.dashboard;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum DashboardSortBy implements SortByEnum {
  SID(DashboardSetting_.SID),
  NAME(DashboardSetting_.NAME),
  DESCRIPTION(DashboardSetting_.DESCRIPTION),
  CREATION_DATE(DashboardSetting_.CREATION_DATE),
  MODIFICATION_DATE(DashboardSetting_.MODIFIED_DATE);

  private final String field;

  DashboardSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
