package com.vinkey.restapi.dashboard;

import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import com.vinkey.restapi.dashboard.exception.DashboardSettingNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

@Service
public class DashboardSettingService {

  private final DashboardSettingRepository dashboardSettingRepository;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public DashboardSettingService(DashboardSettingRepository dashboardSettingRepository) {
    this.dashboardSettingRepository = dashboardSettingRepository;
  }

  @PostAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'STATS_READ')
  """)
  public DashboardSetting read(Long id, Long tenantId) {
    return dashboardSettingRepository
        .findByIdAndTenantId(id, tenantId)
        .orElseThrow(() -> new DashboardSettingNotFoundException(id));
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'STATS_READ')
      OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'STATS_READ')
      OR @membershipService.hasPrivilege(
        @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"),
        authentication.principal.userId,
        'STATS_READ'
      )
      OR @membershipService.hasPrivilege(
        @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"),
        authentication.principal.userId,
        'STATS_READ'
      )
  """)
  public Page<DashboardSetting> readPage(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      Long createdBy,
      String search,
      String sort,
      String filter,
      Long pageNumber,
      Long pageSize) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    List<Order> sortByList =
        sort != null
            ? SortDecoder.decode(sort, DashboardSortBy.class).stream().toList()
            : new ArrayList<>();

    Sort sortBy = Sort.by(Order.desc(DashboardSetting_.ID));

    if (!sortByList.isEmpty()) {
      sortBy = Sort.by(sortByList);
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    DashboardSettingSpecificationBuilder builder =
        new DashboardSettingSpecificationBuilder()
            .tenant(tenantId)
            .group(groupId)
            .createdBy(createdBy)
            .search(search)
            .ancestorGroup(ancestorGroupId);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return dashboardSettingRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#dashboardSetting.getGroup().getId(), authentication.principal.userId, 'STATS_READ')")
  public DashboardSetting create(DashboardSetting dashboardSetting) {
    return dashboardSettingRepository.save(dashboardSetting);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') OR @membershipService.hasPrivilege(#dashboardSetting.getGroup().getId(), authentication.principal.userId, 'STATS_READ')")
  public Long update(DashboardSetting dashboardSetting) {
    return dashboardSettingRepository.save(dashboardSetting).getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') OR @membershipService.hasPrivilege(#dashboardSetting.getGroup().getId(), authentication.principal.userId, 'STATS_READ')")
  public void delete(DashboardSetting dashboardSetting) {
    dashboardSettingRepository.delete(dashboardSetting);
  }
}
