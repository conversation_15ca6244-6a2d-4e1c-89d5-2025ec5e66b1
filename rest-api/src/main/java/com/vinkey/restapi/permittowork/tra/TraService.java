package com.vinkey.restapi.permittowork.tra;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import com.vinkey.restapi.permittowork.tra.row.TraRowRepository;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import com.vinkey.restapi.permittowork.workpermit.WorkPermit;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitStatus;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class TraService {
  private final TraRepository traRepository;
  private final TraRowRepository traRowRepository;
  private final ITemplateEngine templateEngine;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public TraService(
      TraRepository traRepository,
      TraRowRepository traRowRepository,
      ITemplateEngine templateEngine) {
    this.traRepository = traRepository;
    this.traRowRepository = traRowRepository;
    this.templateEngine = templateEngine;
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege({returnObject.group.id, returnObject.workPermit?.group?.id, returnObject.workPermit?.locationGroup?.id}, authentication.principal.userId, 'WORKPERMIT_READ')")
  @Transactional(readOnly = true)
  public Tra getTraById(Long id, Long tenantId) {
    Tra tra =
        traRepository
            .findByIdAndTenantId(id, tenantId)
            .orElseThrow(() -> new RuntimeException("TRA not found"));
    tra.setTraRows(traRowRepository.findAllByTraID(id));
    return tra;
  }

  public Tra attachTraToWorkPermit(Long traId, WorkPermit workPermit) {
    if (traId == null || workPermit == null) {
      throw new RuntimeException("TRA or WorkPermit not found");
    }

    Tra tra =
        traRepository.findById(traId).orElseThrow(() -> new RuntimeException("TRA not found"));

    if (tra.getWorkPermit() != null) {
      throw new RuntimeException("TRA already attached to another WorkPermit");
    }

    tra.setWorkPermit(workPermit);
    return traRepository.save(tra);
  }

  public Tra detachTraFromWorkPermit(Long traId) {
    if (traId == null) {
      throw new RuntimeException("TRA not found");
    }

    Tra tra =
        traRepository.findById(traId).orElseThrow(() -> new RuntimeException("TRA not found"));
    tra.setWorkPermit(null);
    return traRepository.save(tra);
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'WORKPERMIT_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'WORKPERMIT_READ')
      OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'WORKPERMIT_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'WORKPERMIT_READ')
    """)
  public Page<Tra> getAllTra(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      Long createdBy,
      WorkPermitStatus statusNot,
      Boolean hasWorkPermit,
      Long dateLte,
      Long dateGte,
      String search,
      String filter,
      String sort,
      Long pageNumber,
      Long pageSize) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    List<Order> sortByList =
        sort != null
            ? SortDecoder.decode(sort, TraSortBy.class).stream().toList()
            : new ArrayList<>();
    Sort sortBy = Sort.by(Tra_.ID).descending();
    if (!sortByList.isEmpty()) {
      sortBy = Sort.by(sortByList);
    }

    Pageable page = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    TraSpecificationBuilder builder =
        new TraSpecificationBuilder()
            .tenantId(tenantId)
            .groupId(groupId)
            .ancestorGroupId(ancestorGroupId)
            .createdBy(createdBy)
            .workPermitStatusNot(statusNot)
            .hasWorkPermit(hasWorkPermit)
            .dateLte(dateLte)
            .dateGte(dateGte)
            .distinct()
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }

    return traRepository.findAll(builder.build(), page);
  }

  @Transactional
  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#tra.getGroup().getId(), authentication.principal.userId, 'WORKPERMIT_CREATE')")
  public Tra createTra(Tra tra) {
    Tra savedTra = traRepository.save(tra);
    return getTraById(savedTra.getId(), savedTra.getTenant().getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') OR #tra.getCreatedBy().getId().equals(authentication.principal.userId) OR @membershipService.hasPrivilege(#tra.getGroup().getId(), authentication.principal.userId, 'TRA_UPDATE')")
  @Transactional
  public Tra updateTra(Tra tra) {
    if (tra.getWorkPermit() != null && Boolean.TRUE.equals(tra.getWorkPermit().getLocked())) {
      throw new TraNotUpdatableException(tra.getId(), "it's attached to a locked work permit");
    }
    Tra savedTra = traRepository.save(tra);
    return getTraById(savedTra.getId(), savedTra.getTenant().getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or #tra.getCreatedBy().getId().equals(authentication.principal.userId)")
  public void deleteTra(Tra tra) {
    if (tra.getWorkPermit() != null && Boolean.TRUE.equals(tra.getWorkPermit().getLocked())) {
      throw new TraNotDeletableException(tra.getId(), "it's attached to a locked work permit");
    }
    traRepository.delete(tra);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege({#tra.group.id, #tra.workPermit?.group?.id, #tra.workPermit?.locationGroup?.id}, authentication.principal.userId, 'WORKPERMIT_READ')")
  public void generateTraPdf(Tra tra, PrintSettings printSettings, OutputStream out) {
    Context context = new Context();
    context.setVariable("tra", tra);
    context.setVariable("workPermit", tra.getWorkPermit());
    context.setVariable("printSize", printSettings.getPrintSize());

    String html = templateEngine.process("pdf/tra.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("tra.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }
}
