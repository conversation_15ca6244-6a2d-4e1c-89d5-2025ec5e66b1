package com.vinkey.restapi.permittowork.workpermit;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.tenant.Tenant;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitChange;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitCreate;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitListRead;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitRead;
import com.vinkey.restapi.permittowork.workpermit.dto.WorkPermitUpdate;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettingsService;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/work-permits")
public class WorkPermitController {
  public final WorkPermitService workPermitService;
  private final WorkPermitMapper workPermitMapper;
  private final WorkPermitHistoryService workPermitHistoryService;
  private final PrintSettingsService printSettingsService;
  private final FileService fileService;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber;

  public WorkPermitController(
      WorkPermitService workPermitService,
      FileService fileService,
      WorkPermitHistoryService workPermitHistoryService,
      PrintSettingsService printSettingsService) {
    this.workPermitService = workPermitService;
    this.workPermitMapper = WorkPermitMapper.INSTANCE;
    this.workPermitHistoryService = workPermitHistoryService;
    this.printSettingsService = printSettingsService;
    this.fileService = fileService;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public WorkPermitRead createWorkPermit(
      @Valid @RequestBody WorkPermitCreate workPermitCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    WorkPermit workPermitEntity = workPermitMapper.workPermitCreateToWorkPermit(workPermitCreate);
    Tenant tenant = new Tenant();
    tenant.setId(jwtDetails.getTenantId());
    workPermitEntity.setTenant(tenant);
    WorkPermit createdWorkPermit =
        workPermitService.getWorkPermit(
            workPermitService.createWorkPermit(workPermitEntity), jwtDetails.getTenantId());

    createdWorkPermit.getDocuments().forEach((p) -> p.setFile(fileService.setUrl(p.getFile())));
    createdWorkPermit.setFiles(
        fileService.setUrls(createdWorkPermit.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return workPermitMapper.workPermitToWorkPermitRead(createdWorkPermit);
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<WorkPermitListRead> getWorkPermits(
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) WorkPermitStatus status,
      @RequestParam(required = false) WorkPermitStatus statusNot,
      @RequestParam(required = false) RiskCategory riskCategory,
      @RequestParam(required = false) Set<Long> workMethodIds,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) Long lototoPlan,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long creationDateGte,
      @RequestParam(required = false) Long creationDateLte,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) List<Long> candidateUsers,
      @RequestParam(required = false) Long locationOwner,
      @RequestParam(required = false) Long locationOwnerAncestor,
      @RequestParam(required = false) Long locationId,
      @RequestParam(required = false) Long ancestorLocationId,
      @RequestParam(required = false) Long excludeUser,
      @RequestParam(required = false) Long pageSize,
      @RequestParam(required = false) Long pageNumber,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Page<WorkPermit> workPermitEntities =
        workPermitService.getWorkPermits(
            jwtDetails.getTenantId(),
            groupId,
            status,
            statusNot,
            riskCategory,
            workMethodIds,
            createdBy,
            lototoPlan,
            creationDateGte,
            creationDateLte,
            search,
            filter,
            sort,
            pageSize,
            pageNumber,
            candidateGroups,
            candidateUsers,
            ancestorGroupId,
            locationOwner,
            locationOwnerAncestor,
            locationId,
            ancestorLocationId,
            excludeUser);
    return workPermitMapper.workPermitsToWorkPermitReads(workPermitEntities);
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public WorkPermitRead updateWorkPermit(
      @PathVariable Long id,
      @Valid @RequestBody WorkPermitUpdate workPermitUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    WorkPermit workPermitEntity = workPermitService.getWorkPermit(id, jwtDetails.getTenantId());
    workPermitMapper.workPermitUpdateToWorkPermit(workPermitEntity, workPermitUpdate);
    workPermitService.updateWorkPermit(workPermitEntity);
    WorkPermit updatedWorkPermitEntity =
        workPermitService.getWorkPermit(id, jwtDetails.getTenantId());
    updatedWorkPermitEntity
        .getDocuments()
        .forEach((p) -> p.setFile(fileService.setUrl(p.getFile())));
    updatedWorkPermitEntity.setFiles(
        fileService.setUrls(updatedWorkPermitEntity.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return workPermitMapper.workPermitToWorkPermitRead(updatedWorkPermitEntity);
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public WorkPermitRead getWorkPermit(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    WorkPermit membershipEntity = workPermitService.getWorkPermit(id, jwtDetails.getTenantId());
    membershipEntity.getDocuments().forEach((p) -> p.setFile(fileService.setUrl(p.getFile())));
    membershipEntity.setFiles(
        fileService.setUrls(membershipEntity.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return workPermitMapper.workPermitToWorkPermitRead(membershipEntity);
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void cancelWorkPermit(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    WorkPermit workPemitEntity = workPermitService.getWorkPermit(id, jwtDetails.getTenantId());
    workPermitService.cancelWorkPermit(workPemitEntity);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateWorkPermit(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @RequestParam(required = false) Boolean copy,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    WorkPermit workPermit = workPermitService.getWorkPermit(id, jwtDetails.getTenantId());
    PrintSettings printSettings =
        printSettingsService.getPrintSettingsById(jwtDetails.getTenantId());
    printSettings.setLmra(fileService.setUrl(printSettings.getLmra()));

    String filename =
        !copy
            ? "Work permit " + workPermit.getSid() + " (Master).pdf"
            : "Work permit " + workPermit.getSid() + " (Copy).pdf";
    boolean isCopy = copy != null && copy;
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
    workPermitService.generateWorkPermit(
        workPermit, printSettings, timeZone, isCopy, response.getOutputStream());
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<WorkPermitChange> getWorkPermitHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return workPermitHistoryService.getHistory(id, jwtDetails.getTenantId());
  }
}
