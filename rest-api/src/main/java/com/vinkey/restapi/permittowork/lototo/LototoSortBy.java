package com.vinkey.restapi.permittowork.lototo;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum LototoSortBy implements SortByEnum {
  ID(Lototo_.ID),
  SID(Lototo_.SID),
  TITLE(Lototo_.TITLE),
  DESCRIPTION(Lototo_.DESCRIPTION),
  STATUS(Lototo_.STATUS),
  LOCK_NUMBER(Lototo_.LOCK_NUMBER),
  PLANNED_DATE(Lototo_.PLANNED_DATE),
  CREATED_AT(Lototo_.CREATION_DATE),
  UPDATED_AT(Lototo_.MODIFIED_DATE),
  LOCKED(Lototo_.LOCKED);

  private final String field;

  LototoSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
