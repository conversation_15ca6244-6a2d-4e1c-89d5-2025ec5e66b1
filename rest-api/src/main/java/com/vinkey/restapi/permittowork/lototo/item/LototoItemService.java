package com.vinkey.restapi.permittowork.lototo.item;

import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import com.vinkey.restapi.permittowork.lototo.item.exception.LototoItemLockedException;
import com.vinkey.restapi.permittowork.lototo.item.exception.LototoItemNotFoundException;
import com.vinkey.restapi.permittowork.lototo.method.exception.LototoItemMethodStatusNotFoundException;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import org.flowable.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class LototoItemService {
  private final LototoItemRepository lototoItemRepository;
  private final RuntimeService runtimeService;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public LototoItemService(
      LototoItemRepository lototoItemRepository, RuntimeService runtimeService) {
    this.lototoItemRepository = lototoItemRepository;
    this.runtimeService = runtimeService;
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(#ancestorId, authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorId"), authentication.principal.userId, 'LOTOTO_READ')
    """)
  public Page<LototoItem> readAll(
      Long tenantId,
      Long groupId,
      Long ancestorId,
      LototoItemStatus status,
      LototoItemStatus statusNot,
      List<String> candidateGroups,
      List<Long> candidateUsers,
      Long createdBy,
      String search,
      String filter,
      String sort,
      Long pageSize,
      Long pageNumber) {

    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, LototoItem_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          SortDecoder.decode(sort, LototoItemSortBy.class).stream().toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    LototoItemSpecificationBuilder builder =
        new LototoItemSpecificationBuilder()
            .tenant(tenantId)
            .group(groupId)
            .ancestorGroup(ancestorId)
            .status(status)
            .statusNot(statusNot)
            .candidateGroups(candidateGroups)
            .candidateUsers(candidateUsers)
            .createdBy(createdBy)
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return lototoItemRepository.findAll(builder.build(), pageable);
  }

  @PostAuthorize(
      """
    hasRole('TENANT_ADMIN')
    OR @membershipService.hasPathPrivilege(returnObject.lototo.group.id, authentication.principal.userId, 'LOTOTO_READ')
  """)
  public LototoItem read(Long id, Long tenantId) {
    return lototoItemRepository
        .findByIdAndTenantId(id, tenantId)
        .orElseThrow(() -> new LototoItemNotFoundException(id));
  }

  @PreAuthorize(
      """
    hasRole('TENANT_ADMIN')
    OR @membershipService.hasPrivilege(#lototoItem.lototo.group.id, authentication.principal.userId, 'LOTOTO_UPDATE')
  """)
  @Transactional
  public LototoItem update(LototoItem lototoItem) {
    if (lototoItem.getStatus() == LototoItemStatus.CLOSED
        || lototoItem.getStatus() == LototoItemStatus.CANCELED) {
      throw new LototoItemLockedException(lototoItem.getId());
    }

    if (!lototoItem.isValid()) {
      throw new LototoItemMethodStatusNotFoundException();
    }

    lototoItemRepository.save(lototoItem);
    return lototoItem;
  }

  @Transactional
  public void addProcessVariables(LototoItem lototoItem, Map<String, Object> variables) {
    runtimeService.setVariables(lototoItem.getProcessInstance().getId(), variables);
  }

  @Transactional
  public void addVariables(Map<LototoItem, Map<String, Object>> lotoItemVariableMap) {
    for (Map.Entry<LototoItem, Map<String, Object>> entry : lotoItemVariableMap.entrySet()) {
      LototoItem lototoItem = entry.getKey();
      Map<String, Object> variables = entry.getValue();
      addProcessVariables(lototoItem, variables);
    }
  }

  @Transactional
  public void lototo(Long id, Long tenantId, String lockNumber, String additionalInfo) {
    LototoItem lototoItem = read(id, tenantId);
    lototoItem.setLockNumber(lockNumber);
    lototoItem.setAdditionalInfo(additionalInfo);
    lototoItemRepository.save(lototoItem);
  }
}
