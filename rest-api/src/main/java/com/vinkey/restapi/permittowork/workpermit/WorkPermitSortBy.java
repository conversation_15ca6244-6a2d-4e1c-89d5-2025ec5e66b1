package com.vinkey.restapi.permittowork.workpermit;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum WorkPermitSortBy implements SortByEnum {
  ID(WorkPermit_.ID),
  SID(WorkPermit_.SID),
  NAME(WorkPermit_.NAME),
  RISK_CATEGORY(WorkPermit_.RISK_CATEGORY),
  STATUS(WorkPermit_.STATUS),
  START_TIME(WorkPermit_.START_TIME),
  END_TIME(WorkPermit_.END_TIME),
  CREATION_DATE(WorkPermit_.CREATION_DATE),
  MODIFIED_DATE(WorkPermit_.MODIFIED_DATE);

  private final String field;

  WorkPermitSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
