package com.vinkey.restapi.permittowork.lototo;

import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.common.persistence.sort.SortDecoder;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.permittowork.lototo.exception.LototoLockedException;
import com.vinkey.restapi.permittowork.lototo.exception.LototoNotFoundException;
import com.vinkey.restapi.permittowork.lototo.exception.LototonotDeletableException;
import com.vinkey.restapi.permittowork.lototo.item.LototoItem;
import com.vinkey.restapi.permittowork.lototo.item.LototoItemProcessService;
import com.vinkey.restapi.permittowork.lototo.item.LototoItemStatus;
import com.vinkey.restapi.permittowork.lototo.method.exception.LototoItemMethodStatusNotFoundException;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class LototoService {

  private final LototoRepository lototoRepository;
  private final LototoProcessService lototoProcessService;
  private final LototoItemProcessService lototoItemProcessService;
  private final LototoAssembler lototoAssembler;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public LototoService(
      LototoRepository lototoRepository,
      LototoProcessService lototoProcessService,
      LototoItemProcessService lototoItemProcessService,
      LototoAssembler lototoAssembler) {
    this.lototoRepository = lototoRepository;
    this.lototoProcessService = lototoProcessService;
    this.lototoItemProcessService = lototoItemProcessService;
    this.lototoAssembler = lototoAssembler;
  }

  @PostAuthorize(
      """
    hasRole('TENANT_ADMIN')
    OR @membershipService.hasPathPrivilege(returnObject.group.id, authentication.principal.userId, 'LOTOTO_READ')
    """)
  public Lototo read(Long id, Long tenantId) {
    return lototoRepository
        .findByIdAndTenantId(id, tenantId)
        .orElseThrow(() -> new LototoNotFoundException(id));
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'LOTOTO_READ')
      OR @membershipService.hasPrivilege(@filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'LOTOTO_READ')

    """)
  public Page<Lototo> readPage(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      List<String> candidateGroups,
      LototoStatus status,
      String search,
      String filter,
      String sort,
      Long pageSize,
      Long pageNumber) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, Lototo_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders = SortDecoder.decode(sort, LototoSortBy.class).stream().toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);
    LototoSpecificationBuilder builder =
        new LototoSpecificationBuilder()
            .tenant(tenantId)
            .group(groupId)
            .ancestorGroup(ancestorGroupId)
            .candidateGroups(candidateGroups)
            .status(status)
            .search(search);
    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }

    return lototoRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      """
    hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#lototo.getGroup().getId(), authentication.principal.userId, 'LOTOTO_CREATE')
    """)
  @Transactional
  public Long create(Lototo lototo) {
    ProcessInstanceCustom processInstance = lototoProcessService.createProcess(lototo);
    lototo.setProcessInstance(processInstance);

    lototoAssembler.assemble(lototo);

    if (!lototo.isValid()) {
      throw new LototoItemMethodStatusNotFoundException();
    }

    lototoRepository.saveAndFlush(lototo);
    lototoProcessService.initProcessVariables(lototo);

    return lototo.getId();
  }

  @PreAuthorize(
      """
    hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#nLototo.getGroup().getId(), authentication.principal.userId, 'LOTOTO_UPDATE')
    """)
  @Transactional
  public Long update(Lototo nLototo) {
    Lototo oLototo = read(nLototo.getId(), nLototo.getTenant().getId());

    lototoAssembler.assemble(nLototo);

    if (!nLototo.isValid()) {
      throw new LototoItemMethodStatusNotFoundException();
    }

    if (nLototo.hasProcessEnded()) {
      throw new LototoLockedException(nLototo.getId());
    }

    if (nLototo.getLocked() && Objects.equals(oLototo.getLockNumber(), nLototo.getLockNumber())) {
      throw new LototoLockedException(nLototo.getId());
    }

    if (nLototo.getLocked() && !Objects.equals(oLototo.getLockNumber(), nLototo.getLockNumber())) {
      String lockNumber = nLototo.getLockNumber();
      nLototo = oLototo;
      nLototo.setLockNumber(lockNumber);
      lototoProcessService.updateBoxNumberVariable(
          nLototo.getProcessInstance().getId(), lockNumber);
    } else {
      lototoItemProcessService.updateVariables(oLototo.getItems(), nLototo.getItems());
      lototoProcessService.updateVariables(oLototo, nLototo);
    }

    lototoRepository.save(nLototo);
    return nLototo.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#lototo.getGroup().getId(), authentication.principal.userId, 'LOTOTO_DELETE')")
  public Boolean isDeletable(Lototo lototo) {
    return !lototoRepository.existsAnyRelationsById(lototo.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#lototo.getGroup().getId(), authentication.principal.userId, 'LOTOTO_DELETE')")
  public void delete(Lototo lototo) {
    if (!isDeletable(lototo)) {
      throw new LototonotDeletableException(lototo.getId());
    }
    lototoRepository.delete(lototo);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#lototo.getGroup().getId(), authentication.principal.userId, 'LOTOTO_CANCEL') or #lototo.getCreatedBy().getId().equals(authentication.principal.userId)")
  @Transactional
  public void cancel(Lototo lototo) {
    lototo.setLocked(true);
    lototo.setStatus(LototoStatus.CANCELED);
    lototoRepository.save(lototo);
    if (lototo.getProcessInstance() != null) {
      lototoProcessService.removeProcess(lototo);
    }
  }

  @Transactional
  public void lock(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototo.setLocked(true);
    lototoRepository.save(lototo);
  }

  @Transactional
  public void approve(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototo.setStatus(LototoStatus.APPROVED);
    lototoRepository.save(lototo);
  }

  @Transactional
  public void approveAll(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);

    lototo.setStatus(LototoStatus.APPROVED);
    lototoProcessService.checkAllItemsCompleted(lototo);

    for (LototoItem lototoItem : lototo.getItems()) {
      lototoItem.setStatus(LototoItemStatus.APPROVED);
      ProcessInstance processInstance = lototoItemProcessService.createProcess(lototoItem);
      lototoItem.attachProccessInstance(processInstance);
    }

    for (LototoItem lototoItem : lototo.getItems()) {
      lototoItemProcessService.initProcessVariables(lototoItem);
    }

    lototoRepository.save(lototo);
  }

  @Transactional
  public void energyFree(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototo.setStatus(LototoStatus.ENERGY_FREE);
    lototoRepository.save(lototo);
  }

  @Transactional
  public void close(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototo.setStatus(LototoStatus.CLOSED);
    lototoRepository.save(lototo);
  }

  @Transactional
  public void submit(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototo.setStatus(LototoStatus.SUBMITTED);
    lototoRepository.save(lototo);
  }

  @Transactional
  public void checkAllLototoItemsCompleted(Long id, Long tenantId) {
    Lototo lototo = read(id, tenantId);
    lototoProcessService.checkAllItemsCompleted(lototo);
  }
}
