package com.vinkey.restapi.permittowork.tra;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum TraSortBy implements SortByEnum {
  SID(Tra_.SID),
  NAME(Tra_.NAME),
  CREATION_DATE(Tra_.CREATION_DATE),
  MODIFIED_DATE(Tra_.MODIFIED_DATE);

  private final String field;

  TraSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
