package com.vinkey.restapi.permittowork.lototo.item;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;
import com.vinkey.restapi.permittowork.lototo.Lototo_;

public enum LototoItemSortBy implements SortByEnum {
  ID(LototoItem_.ID),
  SID(LototoItem_.SID),
  DESCRIPTION(LototoItem_.DESCRIPTION),
  STATUS(LototoItem_.STATUS),
  LOCK_NUMBER(LototoItem_.LOCK_NUMBER),
  CREATED_AT(LototoItem_.CREATION_DATE),
  UPDATED_AT(LototoItem_.MODIFIED_DATE),
  PLANNED_DATE(LototoItem_.LOTOTO + '.' + Lototo_.PLANNED_DATE);

  private final String field;

  LototoItemSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
