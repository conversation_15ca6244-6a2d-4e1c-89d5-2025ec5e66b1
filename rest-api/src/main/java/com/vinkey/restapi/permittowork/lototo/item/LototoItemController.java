package com.vinkey.restapi.permittowork.lototo.item;

import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.permittowork.lototo.item.dto.LototoItemChange;
import com.vinkey.restapi.permittowork.lototo.item.dto.LototoItemRead;
import com.vinkey.restapi.permittowork.lototo.item.dto.LototoItemUpdate;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/lototo-plan/items")
public class LototoItemController {
  private final LototoItemService lototoItemService;
  private final LototoItemMapper lototoItemMapper;
  private final LototoItemHistoryService lototoItemHistoryService;
  private final LototoItemPdfService lototoItemPdfService;

  public LototoItemController(
      LototoItemService lototoItemService,
      LototoItemHistoryService lototoItemHistoryService,
      LototoItemPdfService lototoItemPdfService) {
    this.lototoItemService = lototoItemService;
    lototoItemMapper = LototoItemMapper.INSTANCE;
    this.lototoItemHistoryService = lototoItemHistoryService;
    this.lototoItemPdfService = lototoItemPdfService;
  }

  @GetMapping
  @ResponseStatus(code = HttpStatus.OK)
  public PaginatedResult<LototoItemRead> getLototoItems(
      @RequestParam(required = false) Long tenantId,
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorId,
      @RequestParam(required = false) LototoItemStatus status,
      @RequestParam(required = false) LototoItemStatus statusNot,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) List<Long> candidateUsers,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageSize,
      @RequestParam(required = false) Long pageNumber,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<LototoItem> lototoItems =
        lototoItemService.readAll(
            tenantId,
            groupId,
            ancestorId,
            status,
            statusNot,
            candidateGroups,
            candidateUsers,
            createdBy,
            search,
            filter,
            sort,
            pageSize,
            pageNumber);
    return lototoItemMapper.pageLototoItemsToPaginatedLototoItemReads(lototoItems);
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public LototoItemRead read(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    return lototoItemMapper.lotoItemToLototoItemRead(lototoItemService.read(id, tenantId));
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public LototoItemRead update(
      @PathVariable Long id,
      @Valid @RequestBody LototoItemUpdate lototoItemUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    LototoItem lototoItem = lototoItemService.read(id, tenantId);
    lototoItemMapper.updateLototoItemWithLototoItemUpdate(lototoItem, lototoItemUpdate);
    LototoItem updatedLototoItem = lototoItemService.update(lototoItem);
    return lototoItemMapper.lotoItemToLototoItemRead(updatedLototoItem);
  }

  @GetMapping("/{id}/history")
  @ResponseStatus(code = HttpStatus.OK)
  public List<LototoItemChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    return lototoItemHistoryService.getHistory(id, tenantId);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generatePdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @RequestParam(required = false) Boolean withFiles,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    LototoItem lototoItem = lototoItemService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=Lototo item " + lototoItem.getSid() + ".pdf");
    lototoItemPdfService.generatePdf(lototoItem, timeZone, response.getOutputStream());
  }
}
