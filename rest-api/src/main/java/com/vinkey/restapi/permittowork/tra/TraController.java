package com.vinkey.restapi.permittowork.tra;

import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.permittowork.tra.dto.TraChange;
import com.vinkey.restapi.permittowork.tra.dto.TraCreate;
import com.vinkey.restapi.permittowork.tra.dto.TraListRead;
import com.vinkey.restapi.permittowork.tra.dto.TraRead;
import com.vinkey.restapi.permittowork.tra.dto.TraUpdate;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitStatus;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettingsService;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/tras")
public class TraController {
  private final TraService traService;
  private final TraHistoryService traHistoryService;
  private final PrintSettingsService printSettingsService;
  private final TraMapper traMapper;

  public TraController(
      TraService traService,
      TraHistoryService traHistoryService,
      PrintSettingsService printSettingsService) {
    this.traService = traService;
    this.traMapper = TraMapper.INSTANCE;
    this.traHistoryService = traHistoryService;
    this.printSettingsService = printSettingsService;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public TraRead createTra(
      @Valid @RequestBody TraCreate traCreate, @AuthenticationPrincipal JwtDetails jwtDetails) {

    Tra referenceTRA = traMapper.traCreateToTRA(traCreate, jwtDetails.getTenantId());
    Tra newTRAEntity = traService.createTra(referenceTRA);

    return traMapper.traToTRARead(newTRAEntity);
  }

  @GetMapping("/{id}")
  public TraRead getTraById(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Tra tra = traService.getTraById(id, jwtDetails.getTenantId());
    return traMapper.traToTRARead(tra);
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<TraChange> getTRAHistoryById(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return traHistoryService.getHistory(id, jwtDetails.getTenantId());
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public TraRead updateTraById(
      @PathVariable Long id,
      @Valid @RequestBody TraUpdate traUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Tra tra = traService.getTraById(id, jwtDetails.getTenantId());
    traMapper.updateTRAFromTRARead(tra, traUpdate);
    return traMapper.traToTRARead(traService.updateTra(tra));
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<TraListRead> getTraList(
      Long groupId,
      Long ancestorGroupId,
      Long createdBy,
      WorkPermitStatus workPermitStatusNot,
      Boolean hasWorkPermit,
      Long dateLte,
      Long dateGte,
      String search,
      String filter,
      String sort,
      Long pageNumber,
      Long pageSize,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<Tra> pages =
        traService.getAllTra(
            jwtDetails.getTenantId(),
            groupId,
            ancestorGroupId,
            createdBy,
            workPermitStatusNot,
            hasWorkPermit,
            dateLte,
            dateGte,
            search,
            filter,
            sort,
            pageNumber,
            pageSize);
    return traMapper.paginatedTRAsToPaginatedTRAListReads(pages);
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteTraById(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Tra tra = traService.getTraById(id, jwtDetails.getTenantId());
    traService.deleteTra(tra);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateTraPdf(
      @PathVariable Long id,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Tra tra = traService.getTraById(id, jwtDetails.getTenantId());
    PrintSettings printSettings =
        printSettingsService.getPrintSettingsById(jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=TRA " + tra.getSid() + ".pdf");
    traService.generateTraPdf(tra, printSettings, response.getOutputStream());
  }
}
