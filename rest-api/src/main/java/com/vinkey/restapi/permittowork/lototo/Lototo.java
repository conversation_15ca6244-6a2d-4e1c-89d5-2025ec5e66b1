package com.vinkey.restapi.permittowork.lototo;

import com.vinkey.restapi.common.file.File;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.tenant.Tenant;
import com.vinkey.restapi.identityandaccess.user.User;
import com.vinkey.restapi.permittowork.lototo.item.LototoItem;
import com.vinkey.restapi.permittowork.lototo.item.LototoItemStatus;
import com.vinkey.restapi.permittowork.workpermit.WorkPermit;
import com.vinkey.restapi.permittowork.workpermit.WorkPermitStatus;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Audited(withModifiedFlag = true)
public class Lototo {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  @Generated(GenerationTime.INSERT)
  private Long sid;

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(nullable = false)
  private Group group;

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(nullable = false)
  private Tenant tenant;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "process_instance_id", nullable = false)
  private ProcessInstanceCustom processInstance;

  @Column(nullable = false)
  @Enumerated(EnumType.STRING)
  private LototoStatus status = LototoStatus.SUBMITTED;

  @Column(nullable = false)
  private Boolean locked = false;

  @Column(nullable = false, length = 255)
  private String title;

  @Column(nullable = false, length = 510)
  private String description;

  // This is functionally box number just named wrong.
  @Column private String lockNumber;

  @Column(nullable = false)
  private Long plannedDate;

  @OneToMany(
      mappedBy = "lototo",
      fetch = FetchType.LAZY,
      cascade = {CascadeType.ALL},
      orphanRemoval = true)
  @OrderBy("order")
  @NotAudited
  private Set<LototoItem> items = new LinkedHashSet<>();

  @ManyToMany
  @JoinTable(
      name = "lototo_document",
      joinColumns = @JoinColumn(name = "lototo_id"),
      inverseJoinColumns = @JoinColumn(name = "file_id"))
  private Set<File> documents = new HashSet<>();

  @NotAudited
  @ManyToMany(mappedBy = "lototoPlans", fetch = FetchType.LAZY)
  private Set<WorkPermit> workPermits = new HashSet<>();

  @Column(name = "created_date", nullable = false, updatable = false)
  @CreatedDate
  private Long creationDate;

  @Column(name = "modified_date", nullable = false)
  @LastModifiedDate
  private Long modifiedDate;

  @CreatedBy
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "created_by", nullable = false, updatable = false)
  private User createdBy;

  @LastModifiedBy
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "modified_by", nullable = false)
  private User modifiedBy;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getSid() {
    return sid;
  }

  public void setSid(Long sid) {
    this.sid = sid;
  }

  public Group getGroup() {
    return group;
  }

  public void setGroup(Group group) {
    this.group = group;
  }

  public Tenant getTenant() {
    return tenant;
  }

  public void setTenant(Tenant tenant) {
    this.tenant = tenant;
  }

  public LototoStatus getStatus() {
    return status;
  }

  public void setStatus(LototoStatus status) {
    this.status = status;
  }

  public Boolean getLocked() {
    return locked;
  }

  public void setLocked(Boolean locked) {
    this.locked = locked;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getLockNumber() {
    return lockNumber;
  }

  public void setLockNumber(String lockNumber) {
    this.lockNumber = lockNumber;
  }

  public Set<File> getDocuments() {
    return documents;
  }

  public void setDocuments(Set<File> documents) {
    this.documents = documents;
  }

  public Long getCreationDate() {
    return creationDate;
  }

  public void setCreationDate(Long creationDate) {
    this.creationDate = creationDate;
  }

  public Long getModifiedDate() {
    return modifiedDate;
  }

  public void setModifiedDate(Long modifiedDate) {
    this.modifiedDate = modifiedDate;
  }

  public User getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(User createdBy) {
    this.createdBy = createdBy;
  }

  public User getModifiedBy() {
    return modifiedBy;
  }

  public void setModifiedBy(User modifiedBy) {
    this.modifiedBy = modifiedBy;
  }

  public Set<LototoItem> getItems() {
    return items;
  }

  public void setItems(Set<LototoItem> items) {
    this.items = items;
  }

  public ProcessInstanceCustom getProcessInstance() {
    return processInstance;
  }

  public void setProcessInstance(ProcessInstanceCustom processInstance) {
    this.processInstance = processInstance;
  }

  public boolean hasActiveItems() {
    return this.items.stream().anyMatch(LototoItem::isActive);
  }

  public boolean allItemsEnergyFree() {
    if (this.items == null || this.items.isEmpty()) {
      return false;
    }
    return this.items.stream()
        .allMatch(lototoItem -> lototoItem.getStatus() == LototoItemStatus.ENERGY_FREE);
  }

  public Long getPlannedDate() {
    return plannedDate;
  }

  public void setPlannedDate(Long plannedDate) {
    this.plannedDate = plannedDate;
  }

  public Set<WorkPermit> getWorkPermits() {
    return workPermits;
  }

  public void setWorkPermits(Set<WorkPermit> workPermits) {
    this.workPermits = workPermits;
  }

  public boolean hasProcessEnded() {
    return this.status == LototoStatus.CLOSED || this.status == LototoStatus.CANCELED;
  }

  public boolean hasActiveIssuedWorkPermits() {
    if (this.workPermits == null || this.workPermits.isEmpty()) {
      return false;
    }

    return this.workPermits.stream()
        .anyMatch(workPermit -> workPermit.getStatus() == WorkPermitStatus.ISSUED);
  }

  public boolean isValid() {
    if (this.items != null && !this.items.isEmpty()) {
      return this.getItems().stream().allMatch(LototoItem::isValid);
    }

    return true;
  }
}
