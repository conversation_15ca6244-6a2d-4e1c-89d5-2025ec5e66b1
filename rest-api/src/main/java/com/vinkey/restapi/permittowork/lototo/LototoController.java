package com.vinkey.restapi.permittowork.lototo;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.permittowork.lototo.dto.LototoChange;
import com.vinkey.restapi.permittowork.lototo.dto.LototoCreate;
import com.vinkey.restapi.permittowork.lototo.dto.LototoRead;
import com.vinkey.restapi.permittowork.lototo.dto.LototoUpdate;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/lototo-plans")
public class LototoController {

  private final LototoService lototoService;
  private final LototoHistoryService lototoHistoryService;
  private final LototoPdfService lototoPdfService;
  private final FileService fileService;
  private final LototoMapper lototoMapper;

  public LototoController(
      LototoService lototoService,
      LototoHistoryService lototoHistoryService,
      LototoPdfService lototoPdfService,
      FileService fileService) {
    this.lototoService = lototoService;
    this.lototoHistoryService = lototoHistoryService;
    this.lototoPdfService = lototoPdfService;
    this.fileService = fileService;
    this.lototoMapper = LototoMapper.INSTANCE;
  }

  @PostMapping
  @ResponseStatus(code = HttpStatus.CREATED)
  public LototoRead create(
      @Valid @RequestBody LototoCreate lototoCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();

    Lototo lototo = lototoMapper.lototoCreateToLototo(lototoCreate, tenantId);

    Long lototoId = lototoService.create(lototo);

    return lototoMapper.lototoToLototoRead(lototoService.read(lototoId, tenantId));
  }

  @GetMapping("/{id}")
  @ResponseStatus(code = HttpStatus.OK)
  public LototoRead read(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Lototo lototo = lototoService.read(id, tenantId);
    lototo.setDocuments(
        fileService.setUrls(lototo.getDocuments().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return lototoMapper.lototoToLototoRead(lototo);
  }

  @GetMapping
  @ResponseStatus(code = HttpStatus.OK)
  public PaginatedResult<LototoRead> readPage(
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) LototoStatus status,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageSize,
      @RequestParam(required = false) Long pageNumber,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Page<Lototo> lototos =
        lototoService.readPage(
            tenantId,
            groupId,
            ancestorGroupId,
            candidateGroups,
            status,
            search,
            filter,
            sort,
            pageSize,
            pageNumber);

    return lototoMapper.pageLototoToPaginatedLototoRead(lototos);
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public LototoRead update(
      @PathVariable Long id,
      @Valid @RequestBody LototoUpdate lototoUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Lototo lototo = lototoService.read(id, tenantId);
    lototoMapper.updateLototoWithLototoUpdate(lototo, lototoUpdate);
    Long updatedLototId = lototoService.update(lototo);
    return lototoMapper.lototoToLototoRead(lototoService.read(updatedLototId, tenantId));
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(code = HttpStatus.OK)
  public Deletable isDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Lototo lototo = lototoService.read(id, tenantId);

    Deletable deletable = new Deletable();
    deletable.setDeletable(lototoService.isDeletable(lototo));
    return deletable;
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(code = HttpStatus.NO_CONTENT)
  public void delete(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Lototo lototo = lototoService.read(id, tenantId);
    lototoService.delete(lototo);
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(code = HttpStatus.OK)
  public void cancel(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    Lototo lototo = lototoService.read(id, tenantId);
    lototoService.cancel(lototo);
  }

  @GetMapping("/{id}/history")
  @ResponseStatus(code = HttpStatus.OK)
  public List<LototoChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Long tenantId = jwtDetails.getTenantId();
    return lototoHistoryService.getHistory(id, tenantId);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateIncidentPdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @RequestParam(required = false) Boolean withFiles,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Lototo lototo = lototoService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=Lototo " + lototo.getSid() + ".pdf");
    lototoPdfService.generatePdf(lototo, timeZone, response.getOutputStream());
  }
}
