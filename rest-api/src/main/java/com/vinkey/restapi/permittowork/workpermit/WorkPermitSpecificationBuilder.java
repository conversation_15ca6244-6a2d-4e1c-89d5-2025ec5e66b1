package com.vinkey.restapi.permittowork.workpermit;

import com.vinkey.restapi.common.persistence.filter.FilterSpecificationBuilder;
import java.util.List;
import java.util.Set;

public class WorkPermitSpecificationBuilder
    extends FilterSpecificationBuilder<WorkPermitSpecification, WorkPermit> {
  public WorkPermitSpecificationBuilder() {
    super(WorkPermitSpecification.class);
  }

  public WorkPermitSpecificationBuilder search(String search) {
    addSpecification(WorkPermitSpecification.search(search));
    return this;
  }

  public WorkPermitSpecificationBuilder creationDateGte(Long date) {
    addSpecification(WorkPermitSpecification.hasCreationDateGte(date));
    return this;
  }

  public WorkPermitSpecificationBuilder creationDateLte(Long date) {
    addSpecification(WorkPermitSpecification.hasCreationDateLte(date));
    return this;
  }

  public WorkPermitSpecificationBuilder status(WorkPermitStatus status) {
    addSpecification(WorkPermitSpecification.hasStatus(status));
    return this;
  }

  public WorkPermitSpecificationBuilder statusNot(WorkPermitStatus status) {
    addSpecification(WorkPermitSpecification.NotStatus(status));
    return this;
  }

  public WorkPermitSpecificationBuilder tenantId(Long tenantId) {
    addSpecification(WorkPermitSpecification.hasTenantId(tenantId));
    return this;
  }

  public WorkPermitSpecificationBuilder createdBy(Long createdBy) {
    addSpecification(WorkPermitSpecification.hasCreatedBy(createdBy));
    return this;
  }

  public WorkPermitSpecificationBuilder lototoPlan(Long lototoPlan) {
    addSpecification(WorkPermitSpecification.hasLototoPlan(lototoPlan));
    return this;
  }

  public WorkPermitSpecificationBuilder riskCategory(RiskCategory riskCategory) {
    addSpecification(WorkPermitSpecification.hasRiskCategory(riskCategory));
    return this;
  }

  public WorkPermitSpecificationBuilder workMethods(Set<Long> workMethodIds) {
    addSpecification(WorkPermitSpecification.hasWorkMethod(workMethodIds));
    return this;
  }

  public WorkPermitSpecificationBuilder groupId(Long groupId) {
    addSpecification(WorkPermitSpecification.hasGroupId(groupId));
    return this;
  }

  public WorkPermitSpecificationBuilder ancestorGroupId(Long ancestorGroupId) {
    addSpecification(WorkPermitSpecification.hasAncestorId(ancestorGroupId));
    return this;
  }

  public WorkPermitSpecificationBuilder candidateGroups(List<String> candidateGroups) {
    addSpecification(WorkPermitSpecification.hasCandidateGroups(candidateGroups));
    return this;
  }

  public WorkPermitSpecificationBuilder excludeUser(Long userId) {
    addSpecification(WorkPermitSpecification.excludeUser(userId));
    return this;
  }

  public WorkPermitSpecificationBuilder candidateUsers(List<Long> candidateUsers) {
    addSpecification(WorkPermitSpecification.hasCandidateUsers(candidateUsers));
    return this;
  }

  public WorkPermitSpecificationBuilder locationOwner(Long locationOwner) {
    addSpecification(WorkPermitSpecification.hasLocationOwner(locationOwner));
    return this;
  }

  public WorkPermitSpecificationBuilder locationOwnerAncestor(Long locationOwnerAncestor) {
    addSpecification(WorkPermitSpecification.hasLocationOwnerAncestor(locationOwnerAncestor));
    return this;
  }

  public WorkPermitSpecificationBuilder ancestorGroupIdOrlocationOwnerAncestor(Long groupId) {
    addSpecification(WorkPermitSpecification.hasAncestorGroupIdOrLocationOwnerAncestor(groupId));
    return this;
  }

  public WorkPermitSpecificationBuilder locationId(Long locationId) {
    addSpecification(WorkPermitSpecification.hasLocationId(locationId));
    return this;
  }

  public WorkPermitSpecificationBuilder ancestorLocationId(Long locationId) {
    addSpecification(WorkPermitSpecification.hasAncestorLocationId(locationId));
    return this;
  }

  public WorkPermitSpecificationBuilder distinct() {
    addSpecification(WorkPermitSpecification.makeDistinct());
    return this;
  }
}
