package com.vinkey.restapi.permittowork.workpermit;

import com.vinkey.restapi.common.persistence.filter.FilterOperation;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.identitylink.runtime.IdentityLinkRuntime;
import com.vinkey.restapi.flowable.identitylink.runtime.IdentityLinkRuntime_;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom_;
import com.vinkey.restapi.flowable.task.runtime.TaskRuntime;
import com.vinkey.restapi.flowable.task.runtime.TaskRuntime_;
import com.vinkey.restapi.identityandaccess.group.Group_;
import com.vinkey.restapi.identityandaccess.group.path.GroupPath;
import com.vinkey.restapi.identityandaccess.group.path.GroupPath_;
import com.vinkey.restapi.location.Location_;
import com.vinkey.restapi.location.path.LocationPath;
import com.vinkey.restapi.location.path.LocationPath_;
import com.vinkey.restapi.permittowork.lototo.Lototo;
import com.vinkey.restapi.permittowork.lototo.Lototo_;
import com.vinkey.restapi.permittowork.workpermit.workmethod.WorkPermitWorkMethod;
import com.vinkey.restapi.permittowork.workpermit.workmethod.WorkPermitWorkMethod_;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.ListJoin;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.SetJoin;
import javax.persistence.criteria.Subquery;
import org.springframework.data.jpa.domain.Specification;

public class WorkPermitSpecification implements Specification<WorkPermit> {
  private SpecFilterCriteria criteria;

  public WorkPermitSpecification(SpecFilterCriteria criteria) {
    this.criteria = criteria;
  }

  static Specification<WorkPermit> makeDistinct() {
    return (root, query, cb) -> {
      query.distinct(true);
      return null;
    };
  }

  static Specification<WorkPermit> hasCreationDateGte(Long date) {
    if (date == null) {
      return null;
    }
    return (root, query, cb) -> cb.greaterThanOrEqualTo(root.get(WorkPermit_.creationDate), date);
  }

  static Specification<WorkPermit> hasCreationDateLte(Long date) {
    if (date == null) {
      return null;
    }
    return (root, query, cb) -> cb.lessThanOrEqualTo(root.get(WorkPermit_.creationDate), date);
  }

  static Specification<WorkPermit> hasStatus(WorkPermitStatus status) {
    if (status == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.status), status);
  }

  static Specification<WorkPermit> hasCreatedBy(Long createdBy) {
    if (createdBy == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.createdBy), createdBy);
  }

  static Specification<WorkPermit> hasRiskCategory(RiskCategory riskCategory) {
    if (riskCategory == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.riskCategory), riskCategory);
  }

  static Specification<WorkPermit> hasWorkMethod(Set<Long> workMethodIds) {
    if (workMethodIds == null || workMethodIds.isEmpty()) {
      return null;
    }
    return (root, query, cb) -> {
      ListJoin<WorkPermit, WorkPermitWorkMethod> workMethodJoin =
          root.join(WorkPermit_.workMethods);
      return cb.and(
          workMethodJoin.get(WorkPermitWorkMethod_.workMethod).in(workMethodIds),
          cb.isTrue(workMethodJoin.get(WorkPermitWorkMethod_.required)));
    };
  }

  static Specification<WorkPermit> hasLototoPlan(Long lototoPlanId) {
    if (lototoPlanId == null) {
      return null;
    }
    return (root, query, cb) -> {
      SetJoin<WorkPermit, Lototo> lototoJoin = root.join(WorkPermit_.lototoPlans);
      return cb.equal(lototoJoin.get(Lototo_.id), lototoPlanId);
    };
  }

  static Specification<WorkPermit> hasGroupId(Long groupId) {
    if (groupId == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.group), groupId);
  }

  static Specification<WorkPermit> hasLocationOwner(Long groupId) {
    if (groupId == null) {
      return null;
    }
    return (root, query, cb) ->
        cb.equal(root.join(WorkPermit_.locationGroup).get(Group_.ID), groupId);
  }

  static Specification<WorkPermit> hasLocationOwnerAncestor(Long groupId) {
    if (groupId == null) {
      return null;
    }

    return (root, query, cb) -> {
      Subquery<Long> subquery = query.subquery(Long.class);
      Root<GroupPath> subRoot = subquery.from(GroupPath.class);
      subquery
          .select(subRoot.get(GroupPath_.DESCENDANT).get(Group_.ID))
          .where(cb.equal(subRoot.get(GroupPath_.ANCESTOR), groupId));
      return cb.in(root.join(WorkPermit_.locationGroup).get(Group_.ID)).value(subquery);
    };
  }

  static Specification<WorkPermit> hasProcessInstanceId(String processInstanceId) {
    if (processInstanceId == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.processInstance), processInstanceId);
  }

  static Specification<WorkPermit> hasAncestorId(Long ancestorId) {
    if (ancestorId == null) {
      return null;
    }
    return (root, query, cb) -> {
      Subquery<Long> subquery = query.subquery(Long.class);
      Root<GroupPath> subRoot = subquery.from(GroupPath.class);
      subquery
          .select(subRoot.get(GroupPath_.DESCENDANT))
          .where(cb.equal(subRoot.get(GroupPath_.ANCESTOR), ancestorId));
      return cb.in(root.join(WorkPermit_.group).get(Group_.ID)).value(subquery);
    };
  }

  static Specification<WorkPermit> hasAncestorGroupIdOrLocationOwnerAncestor(Long groupId) {
    if (groupId == null) {
      return null;
    }
    return Specification.where(hasAncestorId(groupId)).or(hasLocationOwnerAncestor(groupId));
  }

  static Specification<WorkPermit> searchName(String name) {
    if (name == null) {
      return null;
    }
    return (root, query, cb) ->
        cb.like(cb.lower(root.get(WorkPermit_.name)), "%" + name.toLowerCase() + "%");
  }

  static Specification<WorkPermit> hasLocationId(Long locationId) {
    if (locationId == null) {
      return null;
    }
    return (root, query, cb) ->
        cb.equal(root.join(WorkPermit_.location).get(Location_.ID), locationId);
  }

  static Specification<WorkPermit> hasAncestorLocationId(Long locationId) {
    if (locationId == null) {
      return null;
    }
    return (root, query, cb) -> {
      Subquery<Long> subquery = query.subquery(Long.class);
      Root<LocationPath> subRoot = subquery.from(LocationPath.class);
      subquery
          .select(subRoot.get(LocationPath_.DESCENDANT).get(Location_.ID))
          .where(cb.equal(subRoot.get(LocationPath_.ANCESTOR).get(Location_.ID), locationId));
      return cb.in(root.join(WorkPermit_.location).get(Location_.ID)).value(subquery);
    };
  }

  static Specification<WorkPermit> searchSID(String search) {
    if (search == null) {
      return null;
    }
    return (root, query, cb) ->
        cb.like(
            cb.lower(cb.function("convert", String.class, root.get(WorkPermit_.sid))),
            "%" + search.toLowerCase() + "%");
  }

  static Specification<WorkPermit> search(String search) {
    if (search == null) {
      return null;
    }

    // Split search string by spaces and search for each term
    String[] searchArray = search.split(" ");
    Specification<WorkPermit> searchSpec = null;

    for (String searchItem : searchArray) {
      Specification<WorkPermit> termSpec = searchSID(searchItem).or(searchName(searchItem));

      if (searchSpec == null) {
        searchSpec = termSpec;
      } else {
        searchSpec = searchSpec.and(termSpec);
      }
    }

    return searchSpec;
  }

  static Specification<WorkPermit> hasCandidateGroups(List<String> groups) {
    if (groups == null || groups.isEmpty()) {
      return null;
    }

    return (root, query, cb) -> {
      SetJoin<ProcessInstanceCustom, TaskRuntime> taskJoin =
          root.join(WorkPermit_.processInstance, JoinType.LEFT)
              .join(ProcessInstanceCustom_.runtimeTasks, JoinType.LEFT);

      return taskJoin
          .join(TaskRuntime_.identityLinks, JoinType.LEFT)
          .get(IdentityLinkRuntime_.GROUP)
          .in(groups);
    };
  }

  static Specification<WorkPermit> excludeUser(Long userId) {
    if (userId == null) {
      return null;
    }

    return (root, query, cb) -> {
      SetJoin<ProcessInstanceCustom, TaskRuntime> taskJoin =
          root.join(WorkPermit_.processInstance, JoinType.LEFT)
              .join(ProcessInstanceCustom_.runtimeTasks, JoinType.LEFT);

      Subquery<Long> subquery = query.subquery(Long.class);
      Root<IdentityLinkRuntime> subRoot = subquery.from(IdentityLinkRuntime.class);
      subquery
          .select(cb.literal(1L))
          .where(
              cb.and(
                  cb.equal(subRoot.get(IdentityLinkRuntime_.task), taskJoin.get(TaskRuntime_.ID)),
                  cb.equal(subRoot.get(IdentityLinkRuntime_.type), "exclude"),
                  cb.equal(subRoot.get(IdentityLinkRuntime_.user), userId.toString())));
      return cb.not(cb.exists(subquery));
    };
  }

  static Specification<WorkPermit> hasCandidateUsers(List<Long> users) {
    if (users == null || users.isEmpty()) {
      return null;
    }
    List<String> userStrings = users.stream().map(u -> u.toString()).toList();
    return (root, query, cb) ->
        root.join(WorkPermit_.processInstance, JoinType.LEFT)
            .join(ProcessInstanceCustom_.runtimeTasks, JoinType.LEFT)
            .join(TaskRuntime_.identityLinks, JoinType.LEFT)
            .get(IdentityLinkRuntime_.user)
            .in(userStrings);
  }

  static Specification<WorkPermit> NotStatus(WorkPermitStatus status) {
    if (status == null) {
      return null;
    }
    return (root, query, cb) -> cb.notEqual(root.get(WorkPermit_.status), status);
  }

  public static Specification<WorkPermit> hasTenantId(Long tenantId) {
    if (tenantId == null) {
      return null;
    }
    return (root, query, cb) -> cb.equal(root.get(WorkPermit_.tenant), tenantId);
  }

  @Override
  public Predicate toPredicate(
      Root<WorkPermit> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
    if (criteria.getOperation() == FilterOperation.EQUALITY) {
      switch (criteria.getKey()) {
        case "tenantId":
          return hasTenantId(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "search":
          return search(criteria.getValue().toString()).toPredicate(root, query, builder);
        case "groupId":
          return hasGroupId(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "ancestorGroupId":
          return hasAncestorId(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "riskCategory":
          return hasRiskCategory(RiskCategory.valueOf(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "workMethodIds":
          Set<Long> workMethodIds =
              Arrays.stream(criteria.getValue().toString().split(","))
                  .map(Long::parseLong)
                  .collect(Collectors.toSet());
          return hasWorkMethod(workMethodIds).toPredicate(root, query, builder);
        case "status":
          return hasStatus(WorkPermitStatus.valueOf(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "statusNot":
          return NotStatus(WorkPermitStatus.valueOf(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "createdBy":
          return hasCreatedBy(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "candidateGroups":
          List<String> candidateGroups =
              Arrays.stream(criteria.getValue().toString().split(",")).collect(Collectors.toList());
          return hasCandidateGroups(candidateGroups).toPredicate(root, query, builder);
        case "candidateUsers":
          List<Long> candidateUsers =
              Arrays.stream(criteria.getValue().toString().split(","))
                  .map(Long::parseLong)
                  .collect(Collectors.toList());
          return hasCandidateUsers(candidateUsers).toPredicate(root, query, builder);
        case "locationOwner":
          return hasLocationOwner(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "locationOwnerAncestor":
          return hasLocationOwnerAncestor(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "locationId":
          return hasLocationId(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "ancestorLocationId":
          return hasAncestorLocationId(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "creationDateGte":
          return hasCreationDateGte(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "creationDateLte":
          return hasCreationDateLte(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        case "excludeUser":
          return excludeUser(Long.parseLong(criteria.getValue().toString()))
              .toPredicate(root, query, builder);
        default:
          return builder.equal(root.get(criteria.getKey()), criteria.getValue().toString());
      }
    }
    return null;
  }

  public SpecFilterCriteria getCriteria() {
    return criteria;
  }

  public void setCriteria(SpecFilterCriteria criteria) {
    this.criteria = criteria;
  }
}
