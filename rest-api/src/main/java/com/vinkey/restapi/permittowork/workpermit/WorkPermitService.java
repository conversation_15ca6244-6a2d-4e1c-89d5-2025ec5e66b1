package com.vinkey.restapi.permittowork.workpermit;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.datetime.DateTools;
import com.vinkey.restapi.common.file.File;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.location.Location;
import com.vinkey.restapi.location.LocationService;
import com.vinkey.restapi.permittowork.tra.Tra;
import com.vinkey.restapi.permittowork.tra.TraService;
import com.vinkey.restapi.permittowork.workpermit.measure.WorkPermitMeasure;
import com.vinkey.restapi.permittowork.workpermit.measure.WorkPermitMeasureRepository;
import com.vinkey.restapi.permittowork.workpermit.notification.event.WorkPermitApprovedEvent;
import com.vinkey.restapi.permittowork.workpermit.notification.event.WorkPermitCanceledEvent;
import com.vinkey.restapi.permittowork.workpermit.notification.event.WorkPermitPreparedEvent;
import com.vinkey.restapi.permittowork.workpermit.notification.event.WorkPermitUpdatedEvent;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.workmethod.WorkPermitWorkMethod;
import com.vinkey.restapi.permittowork.workpermit.workmethod.WorkPermitWorkMethodRepository;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class WorkPermitService {

  private final WorkPermitRepository workPermitRepository;
  private final RuntimeService runtimeService;
  private final ITemplateEngine templateEngine;
  private final TaskService taskServiceCustom;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final WorkPermitWorkMethodRepository workPermitWorkMethodRepository;
  private final WorkPermitMeasureRepository workPermitMeasureRepository;
  private final TraService traService;
  private final LocationService locationService;

  @Value("${client.baseUrl}")
  private String webClientBaseUrl;

  public WorkPermitService(
      WorkPermitRepository workPermitRepository,
      RuntimeService runtimeService,
      ITemplateEngine templateEngine,
      TaskService taskServiceCustom,
      ApplicationEventPublisher applicationEventPublisher,
      WorkPermitWorkMethodRepository workPermitWorkMethodRepository,
      TraService traService,
      LocationService locationService,
      WorkPermitMeasureRepository workPermitMeasureRepository) {
    this.workPermitRepository = workPermitRepository;
    this.runtimeService = runtimeService;
    this.templateEngine = templateEngine;
    this.traService = traService;
    this.taskServiceCustom = taskServiceCustom;
    this.applicationEventPublisher = applicationEventPublisher;
    this.workPermitWorkMethodRepository = workPermitWorkMethodRepository;
    this.workPermitMeasureRepository = workPermitMeasureRepository;
    this.locationService = locationService;
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#workPermit.getGroup().getId(),authentication.principal.userId, 'WORKPERMIT_CREATE')")
  @Transactional
  public Long createWorkPermit(WorkPermit workPermit) {
    Group getLocationGroup =
        locationService
            .getOwner(
                locationService.getLocation(
                    workPermit.getLocation().getId(), workPermit.getTenant().getId()))
            .getOwner();
    workPermit.setLocationGroup(getLocationGroup);

    if (workPermit.getTra() != null) {
      Tra tra = traService.attachTraToWorkPermit(workPermit.getTra().getId(), workPermit);
      workPermit.setTra(tra);
    }

    String tenantId = workPermit.getTenant().getId().toString();

    ProcessInstanceBuilder instanceBuilder = runtimeService.createProcessInstanceBuilder();
    instanceBuilder.processDefinitionKey("workPermitProcess");
    instanceBuilder.tenantId(tenantId);

    ProcessInstance processInstance = instanceBuilder.start();
    String processInstanceId = processInstance.getProcessInstanceId();

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstanceId);
    processInstanceCustom.setProcessInstanceId(processInstanceId);
    workPermit.setProcessInstance(processInstanceCustom);

    workPermitRepository.save(workPermit);

    Map<String, Object> variables = new HashMap<String, Object>();

    // ? Group Id is the location owner group idand the assigned group is the work permit group id
    variables.put("groupId", workPermit.getLocationGroup().getId());
    variables.put("assignedgroup", workPermit.getGroup().getId());
    variables.put("riskcategory", workPermit.getRiskCategory().getValue());
    variables.put("id", workPermit.getId());
    variables.put("sid", workPermit.getSid());
    variables.put("name", workPermit.getName());
    variables.put("created_by", workPermit.getCreatedBy().getId());
    variables.put("ready", true);

    runtimeService.setVariables(processInstanceId, variables);
    return workPermit.getId();
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'WORKPERMIT_READ') or @membershipService.hasPrivilege(returnObject.locationGroup.id, authentication.principal.userId, 'WORKPERMIT_READ')")
  public WorkPermit getWorkPermit(Long id, Long tenantId) {
    WorkPermit workPermit =
        workPermitRepository
            .findByIdAndTenantId(id, tenantId)
            .orElseThrow(() -> new RuntimeException("Work Permit not found"));

    workPermit.setWorkMethods(workPermitWorkMethodRepository.findAllByWorkPermitId(id));

    workPermit.setMeasures(workPermitMeasureRepository.findAllByWorkPermitId(id));
    return workPermit;
  }

  public Long retrieveFilterKeyValue(List<SpecFilterCriteria> decodedFilter, String key) {
    if (decodedFilter == null) {
      return null;
    }

    List<Long> allEntries =
        decodedFilter.stream()
            .filter(f -> f.getKey().equals(key))
            .map(f -> Long.valueOf((String) f.getValue()))
            .distinct()
            .collect(Collectors.toList());
    if (allEntries.isEmpty()) {
      return null;
    }
    return allEntries.get(0);
  }

  @PreAuthorize(
      """
        hasRole('TENANT_ADMIN')
        OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(@workPermitService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(@workPermitService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(#locationOwner, authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(@workPermitService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "locationOwner"), authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(#locationOwnerAncestor, authentication.principal.userId, 'WORKPERMIT_READ')
        OR @membershipService.hasPrivilege(@workPermitService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "locationOwnerAncestor"), authentication.principal.userId, 'WORKPERMIT_READ')
      """)
  public Page<WorkPermit> getWorkPermits(
      Long tenantId,
      Long groupId,
      WorkPermitStatus status,
      WorkPermitStatus statusNot,
      RiskCategory riskCategory,
      Set<Long> workMethodIds,
      Long createdBy,
      Long lototoPlan,
      Long creationDateGte,
      Long creationDateLte,
      String search,
      String filter,
      String sort,
      Long pageSize,
      Long pageNumber,
      List<String> candidateGroups,
      List<Long> candidateUsers,
      Long ancestorGroupId,
      Long locationOwner,
      Long locationOwnerAncestor,
      Long locationId,
      Long ancestorLocationId,
      Long excludeUser) {
    if (pageSize == null || pageNumber == null) {
      throw new MissingRequestPropertiesException("Page size and page number are required");
    }

    Sort sortBy = Sort.by(Sort.Direction.DESC, WorkPermit_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(
                  sort, WorkPermitSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable page = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    WorkPermitSpecificationBuilder specBuilder =
        new WorkPermitSpecificationBuilder()
            .tenantId(tenantId)
            .groupId(groupId)
            .locationOwner(locationOwner)
            .locationOwnerAncestor(locationOwnerAncestor)
            .locationId(locationId)
            .ancestorLocationId(ancestorLocationId)
            .status(status)
            .riskCategory(riskCategory)
            .workMethods(workMethodIds)
            .search(search)
            .createdBy(createdBy)
            .lototoPlan(lototoPlan)
            .candidateGroups(candidateGroups)
            .excludeUser(excludeUser)
            .candidateUsers(candidateUsers)
            .ancestorGroupId(ancestorGroupId)
            .creationDateGte(creationDateGte)
            .creationDateLte(creationDateLte)
            .distinct()
            .statusNot(statusNot);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        specBuilder.withFilter(filterSpecs.poll());
      }
    }

    return workPermitRepository.findAll(specBuilder.build(), page);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @taskService.checkProcessPermissionAccess(#workPermit.getProcessInstance().getId(),authentication.principal.userId) or @taskService.checkProcessUserCandidate(#workPermit.getProcessInstance().getId(),authentication.principal.userId)")
  @Transactional
  public void updateWorkPermit(WorkPermit workPermit) {
    if (Boolean.TRUE.equals(workPermit.getLocked())) {
      throw new PermitLockedException();
    }

    WorkPermit oldWorkPermit = getWorkPermit(workPermit.getId(), workPermit.getTenant().getId());
    ChangeActivityStateBuilder builder = null;
    if (!Objects.equals(workPermit.getLocation().getId(), oldWorkPermit.getLocation().getId())) {
      Group getLocationGroup =
          locationService
              .getOwner(
                  locationService.getLocation(
                      workPermit.getLocation().getId(), workPermit.getTenant().getId()))
              .getOwner();
      workPermit.setLocationGroup(getLocationGroup);

      String processInstanceId = workPermit.getProcessInstance().getId();
      List<Execution> execution =
          runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list();

      String executionId =
          execution.stream().filter(e -> e.getActivityId() != null).findFirst().get().getId();

      builder = runtimeService.createChangeActivityStateBuilder();

      builder.processInstanceId(processInstanceId);
      builder.moveExecutionToActivityId(executionId, "StartEvent_1");

      // ? Update the location owner group id in the process instance
      runtimeService.setVariable(
          processInstanceId, "groupId", workPermit.getLocationGroup().getId());
    }

    workPermit = updateWorkPermitTra(oldWorkPermit, workPermit);

    if (!Objects.equals(workPermit.getName(), oldWorkPermit.getName())) {
      runtimeService.setVariable(
          workPermit.getProcessInstance().getId(), "name", workPermit.getName());
    }

    runtimeService.setVariable(
        workPermit.getProcessInstance().getId(),
        "riskcategory",
        workPermit.getRiskCategory().getValue());
    workPermitRepository.save(workPermit);

    if (builder != null) {
      builder.changeState();
    }

    applicationEventPublisher.publishEvent(new WorkPermitUpdatedEvent(workPermit.getId()));
  }

  private WorkPermit updateWorkPermitTra(WorkPermit oldWorkPermit, WorkPermit newWorkPermit) {
    Long oldTraId = oldWorkPermit.getTra() != null ? oldWorkPermit.getTra().getId() : null;
    Long newTraId = newWorkPermit.getTra() != null ? newWorkPermit.getTra().getId() : null;

    Boolean isTraAdded = oldTraId == null && newTraId != null;
    Boolean isTraRemoved = oldTraId != null && newTraId == null;
    Boolean isTraReplaced =
        oldTraId != null && newTraId != null && !Objects.equals(oldTraId, newTraId);
    Boolean isTraSame = oldTraId != null && newTraId != null && oldTraId.equals(newTraId);

    if (isTraSame) {
      Tra tra = traService.getTraById(oldTraId, newWorkPermit.getTenant().getId());
      newWorkPermit.setTra(tra);
      return newWorkPermit;
    }

    if (isTraRemoved || isTraReplaced) {
      traService.detachTraFromWorkPermit(oldTraId);
      newWorkPermit.setTra(null);
    }

    if (isTraAdded || isTraReplaced) {
      Tra tra = traService.attachTraToWorkPermit(newTraId, newWorkPermit);
      newWorkPermit.setTra(tra);
    }
    return newWorkPermit;
  }

  @PreAuthorize(
      """
        hasRole('TENANT_ADMIN')
        or #workPermit.getCreatedBy().getId().equals(authentication.principal.userId)
        or @membershipService.hasPrivilege(#workPermit.getLocationGroup().getId(), authentication.principal.userId, 'WORKPERMIT_CANCEL')
    """)
  @Transactional
  public void cancelWorkPermit(WorkPermit workPermit) {
    workPermit.setLocked(true);
    workPermit.setStatus(WorkPermitStatus.CANCELED);
    workPermitRepository.save(workPermit);
    runtimeService.deleteProcessInstance(
        workPermit.getProcessInstance().getId(), "Work Permit canceled by user");
    applicationEventPublisher.publishEvent(new WorkPermitCanceledEvent(workPermit.getId()));
  }

  @Transactional
  public void lockWorkPermit(Long id, Long tenantId) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);

    Map<String, Object> variables = new HashMap<String, Object>();

    Long startTimeWithoutMS = workPemitEntity.getStartTime() / 1000;

    Instant instant = Instant.ofEpochSecond(startTimeWithoutMS - 10800);
    String iso8601Date = instant.toString();
    variables.put("startTimeMinusThree", iso8601Date);

    runtimeService.setVariables(workPemitEntity.getProcessInstance().getId(), variables);

    workPemitEntity.setLocked(true);
    workPermitRepository.save(workPemitEntity);
  }

  @Transactional
  public void issueWorkPermit(
      Long id,
      Long tenantId,
      String holder,
      String additionalRequirements,
      Long actualWorkerCount) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);
    workPemitEntity.setHolder(holder);
    workPemitEntity.setAdditionalRequirements(additionalRequirements);
    workPemitEntity.setActualWorkerCount(actualWorkerCount);
    workPemitEntity.setStatus(WorkPermitStatus.ISSUED);
    workPermitRepository.save(workPemitEntity);
  }

  @Transactional
  public void prepareWorkPermit(Long id, Long tenantId) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);
    workPemitEntity.setStatus(WorkPermitStatus.PREPARED);
    workPermitRepository.save(workPemitEntity);
    applicationEventPublisher.publishEvent(new WorkPermitPreparedEvent(id));
  }

  @Transactional
  public void closeWorkPermit(Long id, Long tenantId) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);
    workPemitEntity.setStatus(WorkPermitStatus.CLOSED);
    workPermitRepository.save(workPemitEntity);
  }

  @Transactional
  public void approveWorkPermit(Long id, Long tenantId) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);
    workPemitEntity.setStatus(WorkPermitStatus.APPROVED);

    Map<String, Object> variables = new HashMap<String, Object>();

    Long endTimeWithOutMS = workPemitEntity.getEndTime() / 1000;

    Instant instant = Instant.ofEpochSecond(endTimeWithOutMS + 10800);
    String iso8601Date = instant.toString();
    variables.put("endTime", iso8601Date);

    runtimeService.setVariables(workPemitEntity.getProcessInstance().getId(), variables);

    workPermitRepository.save(workPemitEntity);
    applicationEventPublisher.publishEvent(new WorkPermitApprovedEvent(id));
  }

  @Transactional
  public void setWorkPermitRiskCategory(Long id, Long tenantId, RiskCategory riskCategory) {
    WorkPermit workPemitEntity = this.getWorkPermit(id, tenantId);
    workPemitEntity.setRiskCategory(riskCategory);
    workPermitRepository.save(workPemitEntity);
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#workPemitEntity.getGroup().getId(), authentication.principal.userId, 'WORKPERMIT_READ') or @membershipService.hasPrivilege(#workPemitEntity.getLocationGroup().getId(), authentication.principal.userId, 'WORKPERMIT_READ')")
  public void generateWorkPermit(
      WorkPermit workPemitEntity,
      PrintSettings printSettings,
      String timeZone,
      boolean isCopy,
      OutputStream out) {
    List<TaskHistory> historyTasks =
        taskServiceCustom.getHistory(
            workPemitEntity.getProcessInstance().getId(), workPemitEntity.getTenant().getId());
    Map<String, List<TaskHistory>> multiValueMap = new HashMap<>();
    historyTasks.stream()
        .forEach(
            task -> {
              if (multiValueMap.containsKey(task.getTaskDefinitionKey()))
                multiValueMap.get(task.getTaskDefinitionKey()).add(task);
              else
                multiValueMap.put(
                    task.getTaskDefinitionKey(), new ArrayList<>(Arrays.asList(task)));
            });

    List<TaskHistory> taskOnly =
        multiValueMap.values().stream()
            .map(
                taskList ->
                    taskList.stream()
                        .filter(
                            task ->
                                task.getEndTime() != null
                                    && !(task.getName().equals("Process feedback")
                                        || task.getName().equals("Revoke")
                                        || task.getName().equals("DE-LOTOTO")
                                        || task.getName().equals("Start closing")))
                        .toList())
            .filter(taskList -> !taskList.isEmpty())
            .map(
                tasks ->
                    Collections.max(
                        tasks, Comparator.comparing(task -> task.getEndTime().getTime())))
            .collect(Collectors.toList());

    if (!workPemitEntity.getStatus().equals(WorkPermitStatus.ISSUED)) {
      taskOnly =
          taskOnly.stream()
              .filter(task -> !task.getName().equals("Issue"))
              .collect(Collectors.toList());
    }

    if (!taskOnly.isEmpty())
      Collections.sort(taskOnly, Comparator.comparing(TaskHistory::getEndTime));

    List<WorkPermitMeasure> requiredMeasures =
        workPemitEntity.getMeasures().stream()
            .filter(WorkPermitMeasure::getRequired)
            .collect(Collectors.toList());
    List<WorkPermitWorkMethod> requiredWorkMethodes =
        workPemitEntity.getWorkMethods().stream()
            .filter(WorkPermitWorkMethod::getRequired)
            .collect(Collectors.toList());

    workPemitEntity.setMeasures(requiredMeasures);
    workPemitEntity.setWorkMethods(requiredWorkMethodes);

    Tra tra = null;

    if (workPemitEntity.getTra() != null) {
      tra =
          traService.getTraById(
              workPemitEntity.getTra().getId(), workPemitEntity.getTenant().getId());
    }

    ZonedDateTime start = DateTools.epochToDate(workPemitEntity.getStartTime() / 1000, timeZone);
    ZonedDateTime end = DateTools.epochToDate(workPemitEntity.getEndTime() / 1000, timeZone);
    ZonedDateTime creationDate =
        DateTools.epochToDate(workPemitEntity.getCreationDate() / 1000, timeZone);

    String startDate = DateTools.dateToDayMonthYearString(start);

    String endDate = DateTools.dateToDayMonthYearString(end);

    String startTime = DateTools.dateToHourMinuteString(start);

    String endTime = DateTools.dateToHourMinuteString(end);

    String creationDateTime = DateTools.dateToDayMonthYearHourMinuteString(creationDate);

    Context context = new Context();
    context.setVariable("riskCategoryName", workPemitEntity.getRiskCategory().getValue());
    context.setVariable("atexZoneName", workPemitEntity.getAtexZone().getValue());
    context.setVariable("uploadedBy", workPemitEntity.getCreatedBy());
    context.setVariable("permit", workPemitEntity);
    context.setVariable("startTime", startTime);
    context.setVariable("endTime", endTime);
    context.setVariable("startDate", startDate);
    context.setVariable("endDate", endDate);
    context.setVariable("creationDateTime", creationDateTime);
    context.setVariable("tasks", taskOnly);
    context.setVariable("tra", tra);
    context.setVariable("workPermit", workPemitEntity);
    context.setVariable("printSize", printSettings.getPrintSize());
    context.setVariable("printCustom", printSettings.getPrintCustom());
    context.setVariable("lmra", printSettings.getLmra());
    context.setVariable("copy", isCopy);
    context.setVariable("title", !isCopy ? "Master for holder" : "Copy for issuer");
    context.setVariable("timeZone", timeZone);

    String locationOwnerGroupId = workPemitEntity.getLocationGroup().getId().toString();
    String id = workPemitEntity.getId().toString();
    String qrCodeUrl =
        String.format(
            "%1$s/groups/%2$s/work-permits/%3$s",
            webClientBaseUrl + "/" + workPemitEntity.getCreatedBy().getTenant().getUrl(),
            locationOwnerGroupId,
            id);
    context.setVariable("qrCode", getQrCode(qrCodeUrl));
    context.setVariable("modelUrl", this.getLocationModelUrl(workPemitEntity.getLocation()));

    String html = templateEngine.process("pdf/work-permit.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();
      builder.withUri("work-permit.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }

  private String getQrCode(String url) {
    int imageSize = 200;
    try {
      BitMatrix matrix =
          new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, imageSize, imageSize);
      ByteArrayOutputStream bos = new ByteArrayOutputStream();
      MatrixToImageWriter.writeToStream(matrix, "png", bos);
      String image = Base64.getEncoder().encodeToString(bos.toByteArray()); // base64 encode
      return image;
    } catch (IOException | WriterException e) {
      throw new PDFException();
    }
  }

  private String getLocationModelUrl(Location location) {
    File file = this.locationService.getLocationScreenshotUrl(location);
    return file != null ? file.getReferenceName() : null;
  }
}
